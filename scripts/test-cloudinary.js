// Simple test script to verify Cloudinary configuration
const fs = require('fs');
const path = require('path');

console.log('Testing Cloudinary Configuration...');

// Load environment variables from .env file
const envPath = path.join(__dirname, '..', '.env');
let cloudName, apiKey, apiSecret;

try {
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');

  envLines.forEach(line => {
    if (line.startsWith('VITE_CLOUDINARY_CLOUD_NAME=')) {
      cloudName = line.split('=')[1];
    } else if (line.startsWith('VITE_CLOUDINARY_API_KEY=')) {
      apiKey = line.split('=')[1];
    } else if (line.startsWith('VITE_CLOUDINARY_API_SECRET=')) {
      apiSecret = line.split('=')[1];
    }
  });
} catch (error) {
  console.error('Error reading .env file:', error.message);
}

console.log('Environment Variables:');
console.log('VITE_CLOUDINARY_CLOUD_NAME:', cloudName ? '✓ Set' : '✗ Missing');
console.log('VITE_CLOUDINARY_API_KEY:', apiKey ? '✓ Set' : '✗ Missing');
console.log('VITE_CLOUDINARY_API_SECRET:', apiSecret ? '✓ Set' : '✗ Missing');

if (cloudName && apiKey && apiSecret) {
  console.log('\n✅ All Cloudinary environment variables are configured!');
  console.log('\nNext steps:');
  console.log('1. Create upload preset "ecopulse_products" in Cloudinary dashboard');
  console.log('2. Set preset to "Unsigned" mode');
  console.log('3. Configure folder as "ecopulse/products"');
  console.log('4. Test image upload through the admin interface');
} else {
  console.log('\n❌ Missing Cloudinary configuration. Please check your .env file.');
}

// Test Cloudinary upload URL generation
if (cloudName) {
  const uploadUrl = `https://api.cloudinary.com/v1_1/${cloudName}/image/upload`;
  console.log('\nCloudinary Upload URL:', uploadUrl);
}

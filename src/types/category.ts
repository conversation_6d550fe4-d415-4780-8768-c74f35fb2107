import { LucideIcon } from 'lucide-react';

export interface Category {
  id: number;
  name: string;
  description: string;
  slug: string;
  icon: LucideIcon | string;
  productCount: number;
  status: 'active' | 'inactive';
  color?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateCategoryData {
  name: string;
  description: string;
  slug: string;
  icon: string;
  status?: 'active' | 'inactive';
}

export interface UpdateCategoryData extends Partial<CreateCategoryData> {
  id: number;
}

export interface CategoryFormData {
  name: string;
  description: string;
  slug: string;
  icon: string;
  status: 'active' | 'inactive';
}

export interface CategoryTableColumn {
  key: keyof Category | 'actions';
  label: string;
  sortable?: boolean;
  width?: string;
}

export interface CategoryFilters {
  search?: string;
  status?: 'active' | 'inactive' | 'all';
  sortBy?: keyof Category;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedCategoriesResponse {
  data: Category[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Icon options for the category form
export interface IconOption {
  value: string;
  label: string;
  icon: LucideIcon;
}

// Available category icons
export const CATEGORY_ICONS = {
  'sun': 'Sun',
  'battery': 'Battery',
  'zap': 'Zap',
  'cpu': 'Cpu',
  'package': 'Package',
  'settings': 'Settings',
  'tool': 'Wrench',
  'cable': 'Cable',
  'power': 'Power',
  'lightbulb': 'Lightbulb',
} as const;

export type CategoryIconKey = keyof typeof CATEGORY_ICONS;

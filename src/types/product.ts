export interface Product {
  id: number;
  name: string;
  description: string;
  category: string;
  categoryId: number;
  price: number;
  costPrice?: number;
  stock: number;
  minStock: number;
  maxStock?: number;
  sku: string;
  barcode?: string;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  images: string[];
  status: 'active' | 'inactive' | 'draft';
  featured?: boolean;
  tags?: string[];
  specifications?: Record<string, string>;
  warranty?: string;
  brand?: string;
  model?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateProductData {
  name: string;
  description: string;
  categoryId: number;
  price: number;
  costPrice?: number;
  stock: number;
  minStock: number;
  maxStock?: number;
  sku: string;
  barcode?: string;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  images?: string[];
  status?: 'active' | 'inactive' | 'draft';
  featured?: boolean;
  tags?: string[];
  specifications?: Record<string, string>;
  warranty?: string;
  brand?: string;
  model?: string;
}

export interface UpdateProductData extends Partial<CreateProductData> {
  id: number;
}

export interface ProductFormData {
  name: string;
  description: string;
  categoryId: number;
  price: number;
  costPrice: number;
  stock: number;
  minStock: number;
  maxStock: number;
  sku: string;
  barcode: string;
  weight: number;
  dimensions: {
    length: number;
    width: number;
    height: number;
  };
  status: 'active' | 'inactive' | 'draft';
  featured: boolean;
  warranty: string;
  brand: string;
  model: string;
  images: string[];
}

export interface ProductFilters {
  search?: string;
  category?: string;
  status?: 'active' | 'inactive' | 'draft' | 'all';
  stockStatus?: 'in-stock' | 'low-stock' | 'out-of-stock' | 'all';
  featured?: boolean;
  sortBy?: keyof Product;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedProductsResponse {
  data: Product[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface ProductTableColumn {
  key: keyof Product | 'actions';
  label: string;
  sortable?: boolean;
  width?: string;
}

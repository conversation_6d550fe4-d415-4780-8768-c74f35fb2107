// Cloudinary configuration and utilities
export const cloudinaryConfig = {
  cloudName: import.meta.env.VITE_CLOUDINARY_CLOUD_NAME,
  apiKey: import.meta.env.VITE_CLOUDINARY_API_KEY,
  apiSecret: import.meta.env.VITE_CLOUDINARY_API_SECRET,
};

// Validate Cloudinary configuration
export const validateCloudinaryConfig = (): boolean => {
  const { cloudName, apiKey, apiSecret } = cloudinaryConfig;
  
  if (!cloudName || !apiKey || !apiSecret) {
    console.error('Missing Cloudinary configuration. Please check your environment variables.');
    return false;
  }
  
  return true;
};

// Generate Cloudinary upload URL
export const getCloudinaryUploadUrl = (): string => {
  return `https://api.cloudinary.com/v1_1/${cloudinaryConfig.cloudName}/image/upload`;
};

// Generate signature for secure uploads (for client-side uploads)
export const generateUploadSignature = (timestamp: number, folder?: string): string => {
  // Note: In production, this should be done on the server side for security
  // For now, we'll use unsigned uploads with upload presets
  return '';
};

// Cloudinary transformation utilities
export const getOptimizedImageUrl = (publicId: string, options?: {
  width?: number;
  height?: number;
  quality?: string;
  format?: string;
}): string => {
  const { cloudName } = cloudinaryConfig;
  const { width, height, quality = 'auto', format = 'auto' } = options || {};
  
  let transformations = `q_${quality},f_${format}`;
  
  if (width) transformations += `,w_${width}`;
  if (height) transformations += `,h_${height}`;
  
  return `https://res.cloudinary.com/${cloudName}/image/upload/${transformations}/${publicId}`;
};

// Extract public ID from Cloudinary URL
export const extractPublicId = (cloudinaryUrl: string): string => {
  const regex = /\/upload\/(?:v\d+\/)?(.+)\.[^.]+$/;
  const match = cloudinaryUrl.match(regex);
  return match ? match[1] : '';
};

// Check if URL is a Cloudinary URL
export const isCloudinaryUrl = (url: string): boolean => {
  return url.includes('res.cloudinary.com') || url.includes('cloudinary.com');
};

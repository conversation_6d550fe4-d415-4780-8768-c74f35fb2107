import {
  ref,
  push,
  set,
  get,
  remove,
  update,
  onValue,
  off,
  query,
  orderByChild,
  equalTo,
  limitToLast,
  serverTimestamp,
  DataSnapshot
} from 'firebase/database';
import { database } from './firebase';

// Database paths
export const DB_PATHS = {
  CATEGORIES: 'categories',
  PRODUCTS: 'products',
  ORDERS: 'orders',
  CUSTOMERS: 'customers',
  USERS: 'users',
  SETTINGS: 'settings'
} as const;

// Generic database service for Firebase Realtime Database
export class DatabaseService<T extends Record<string, any>> {
  constructor(private path: string) {}

  // Create a new record
  async create(data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const newRef = push(ref(database, this.path));
      const id = newRef.key!;
      
      const recordData = {
        ...data,
        id,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      await set(newRef, recordData);
      return id;
    } catch (error) {
      console.error(`Error creating record in ${this.path}:`, error);
      throw error;
    }
  }

  // Get a record by ID
  async getById(id: string): Promise<T | null> {
    try {
      const snapshot = await get(ref(database, `${this.path}/${id}`));
      return snapshot.exists() ? snapshot.val() : null;
    } catch (error) {
      console.error(`Error getting record ${id} from ${this.path}:`, error);
      throw error;
    }
  }

  // Get all records
  async getAll(): Promise<T[]> {
    try {
      const snapshot = await get(ref(database, this.path));
      if (!snapshot.exists()) return [];
      
      const data = snapshot.val();
      return Object.values(data) as T[];
    } catch (error) {
      console.error(`Error getting all records from ${this.path}:`, error);
      throw error;
    }
  }

  // Update a record
  async update(id: string, data: Partial<Omit<T, 'id' | 'createdAt'>>): Promise<void> {
    try {
      const updateData = {
        ...data,
        updatedAt: serverTimestamp()
      };
      
      await update(ref(database, `${this.path}/${id}`), updateData);
    } catch (error) {
      console.error(`Error updating record ${id} in ${this.path}:`, error);
      throw error;
    }
  }

  // Delete a record
  async delete(id: string): Promise<void> {
    try {
      await remove(ref(database, `${this.path}/${id}`));
    } catch (error) {
      console.error(`Error deleting record ${id} from ${this.path}:`, error);
      throw error;
    }
  }

  // Query records with filters
  async queryByField(field: string, value: any, limit?: number): Promise<T[]> {
    try {
      let dbQuery = query(
        ref(database, this.path),
        orderByChild(field),
        equalTo(value)
      );

      if (limit) {
        dbQuery = query(dbQuery, limitToLast(limit));
      }

      const snapshot = await get(dbQuery);
      if (!snapshot.exists()) return [];

      const data = snapshot.val();
      return Object.values(data) as T[];
    } catch (error) {
      console.error(`Error querying ${this.path} by ${field}:`, error);
      // Fallback to client-side filtering if server-side query fails
      console.warn(`Falling back to client-side filtering for ${this.path}.${field}`);

      const allRecords = await this.getAll();
      const filteredRecords = allRecords.filter((record: any) => record[field] === value);

      return limit ? filteredRecords.slice(0, limit) : filteredRecords;
    }
  }

  // Get recent records
  async getRecent(limit: number = 10): Promise<T[]> {
    try {
      const dbQuery = query(
        ref(database, this.path),
        orderByChild('createdAt'),
        limitToLast(limit)
      );

      const snapshot = await get(dbQuery);
      if (!snapshot.exists()) return [];

      const data = snapshot.val();
      const records = Object.values(data) as T[];
      return records.reverse(); // Most recent first
    } catch (error) {
      console.error(`Error getting recent records from ${this.path}:`, error);
      // Fallback to client-side sorting if server-side query fails
      console.warn(`Falling back to client-side sorting for ${this.path}`);

      const allRecords = await this.getAll();
      // Sort by createdAt (assuming it's a timestamp or ISO string)
      const sortedRecords = allRecords.sort((a: any, b: any) => {
        const aTime = new Date(a.createdAt).getTime();
        const bTime = new Date(b.createdAt).getTime();
        return bTime - aTime; // Most recent first
      });

      return sortedRecords.slice(0, limit);
    }
  }

  // Listen to real-time changes
  onDataChange(callback: (data: T[]) => void): () => void {
    const dbRef = ref(database, this.path);
    
    const unsubscribe = onValue(dbRef, (snapshot) => {
      if (snapshot.exists()) {
        const data = snapshot.val();
        const records = Object.values(data) as T[];
        callback(records);
      } else {
        callback([]);
      }
    }, (error) => {
      console.error(`Error listening to ${this.path}:`, error);
    });

    // Return unsubscribe function
    return unsubscribe;
  }

  // Listen to a specific record
  onRecordChange(id: string, callback: (data: T | null) => void): () => void {
    const dbRef = ref(database, `${this.path}/${id}`);
    
    const unsubscribe = onValue(dbRef, (snapshot) => {
      const data = snapshot.exists() ? snapshot.val() : null;
      callback(data);
    }, (error) => {
      console.error(`Error listening to ${this.path}/${id}:`, error);
    });

    // Return unsubscribe function
    return () => off(dbRef, 'value', unsubscribe);
  }
}

// Database interfaces for Firebase Realtime Database
export interface DatabaseCategory {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'inactive';
  createdAt: any; // Firebase timestamp
  updatedAt: any; // Firebase timestamp
}

export interface DatabaseProduct {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  categoryId: string;
  stock: number;
  image: string; // Keep for backward compatibility
  images?: string[]; // New field for multiple images
  status: 'active' | 'inactive';
  createdAt: any;
  updatedAt: any;
}

export interface DatabaseOrder {
  id: string;
  orderNumber: string;
  customerId: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  items: Array<{
    productId: string;
    productName: string;
    quantity: number;
    price: number;
    total: number;
  }>;
  subtotal: number;
  tax: number;
  shipping: number;
  total: number;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentMethod?: 'cash' | 'transfer' | 'card' | 'whatsapp';
  source?: 'admin' | 'whatsapp' | 'website';
  notes?: string;
  shippingAddress: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  createdAt: any;
  updatedAt: any;
}

export interface DatabaseSettings {
  id: string;
  userId: string;
  profile: {
    name: string;
    email: string;
    phone: string;
    company: string;
  };
  preferences: {
    theme: 'light' | 'dark' | 'system';
    language: string;
    timezone: string;
    currency: string;
    notifications: {
      email: boolean;
      push: boolean;
      sms: boolean;
    };
  };
  business: {
    companyName: string;
    address: string;
    phone: string;
    email: string;
    website?: string;
    taxId?: string;
  };
  createdAt: any;
  updatedAt: any;
}

// Service instances
export const categoriesDB = new DatabaseService<DatabaseCategory>(DB_PATHS.CATEGORIES);
export const productsDB = new DatabaseService<DatabaseProduct>(DB_PATHS.PRODUCTS);
export const ordersDB = new DatabaseService<DatabaseOrder>(DB_PATHS.ORDERS);
export const settingsDB = new DatabaseService<DatabaseSettings>(DB_PATHS.SETTINGS);

// Helper functions
export const generateOrderNumber = (): string => {
  const timestamp = Date.now().toString();
  const random = Math.random().toString(36).substring(2, 8).toUpperCase();
  return `ORD-${timestamp.slice(-6)}-${random}`;
};

export const convertTimestamp = (timestamp: any): Date => {
  if (timestamp && typeof timestamp === 'object' && timestamp.seconds) {
    return new Date(timestamp.seconds * 1000);
  }
  if (typeof timestamp === 'number') {
    return new Date(timestamp);
  }
  if (typeof timestamp === 'string') {
    return new Date(timestamp);
  }
  return new Date();
};

import { cloudinaryService } from '@/services/cloudinaryService';
import { extractPublicId, isCloudinaryUrl } from './cloudinary';

/**
 * Upload an image file to Cloudinary
 * @param file - The image file to upload
 * @param path - The folder path (e.g., 'products', 'categories')
 * @param fileName - Optional custom filename (not used in Cloudinary, but kept for compatibility)
 * @returns Promise<string> - The secure URL of the uploaded image
 */
export const uploadImage = async (
  file: File,
  path: string = 'products',
  fileName?: string
): Promise<string> => {
  try {
    console.log('Starting Cloudinary image upload:', { fileName: file.name, size: file.size, type: file.type, path });

    // Validate file using Cloudinary service
    const validation = cloudinaryService.validateFile(file);
    if (!validation.isValid) {
      throw new Error(validation.error || 'Invalid file');
    }

    // Upload to Cloudinary
    console.log('Uploading to Cloudinary...');
    const result = await cloudinaryService.uploadImage(file, {
      folder: `ecopulse/${path}`,
      quality: 'auto',
      format: 'auto'
    });

    console.log('Cloudinary upload completed:', {
      public_id: result.public_id,
      secure_url: result.secure_url,
      width: result.width,
      height: result.height,
      format: result.format,
      bytes: result.bytes
    });

    return result.secure_url;
  } catch (error) {
    console.error('Error uploading image to Cloudinary:', error);

    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('Invalid image file')) {
        throw new Error('Upload failed: Please select a valid image file (JPEG, PNG, GIF, or WebP).');
      } else if (error.message.includes('File size')) {
        throw new Error('Upload failed: Image size must be less than 10MB.');
      } else if (error.message.includes('network') || error.message.includes('fetch')) {
        throw new Error('Upload failed: Network error. Please check your internet connection and try again.');
      } else if (error.message.includes('unauthorized') || error.message.includes('401')) {
        throw new Error('Upload failed: Invalid Cloudinary configuration. Please check your credentials.');
      }
    }

    throw error;
  }
};

/**
 * Upload multiple images to Cloudinary
 * @param files - Array of image files to upload
 * @param path - The folder path (e.g., 'products', 'categories')
 * @returns Promise<string[]> - Array of secure URLs
 */
export const uploadMultipleImages = async (
  files: File[],
  path: string = 'products'
): Promise<string[]> => {
  try {
    console.log(`Starting multiple image upload: ${files.length} files to ${path}`);

    const uploadPromises = files.map(file => uploadImage(file, path));
    const urls = await Promise.all(uploadPromises);

    console.log(`Multiple upload completed: ${urls.length} images uploaded`);
    return urls;
  } catch (error) {
    console.error('Error uploading multiple images:', error);
    throw error;
  }
};

/**
 * Delete an image from Cloudinary
 * @param imageUrl - The Cloudinary URL of the image to delete
 */
export const deleteImage = async (imageUrl: string): Promise<void> => {
  try {
    // Check if it's a Cloudinary URL
    if (!isCloudinaryUrl(imageUrl)) {
      console.warn('Attempted to delete non-Cloudinary URL:', imageUrl);
      return;
    }

    // Extract public ID from Cloudinary URL
    const publicId = extractPublicId(imageUrl);
    if (!publicId) {
      throw new Error('Could not extract public ID from Cloudinary URL');
    }

    console.log('Deleting image with public ID:', publicId);

    // Use Cloudinary service to delete
    await cloudinaryService.deleteImage(publicId);

    console.log('Image deletion completed');
  } catch (error) {
    console.error('Error deleting image from Cloudinary:', error);
    throw error;
  }
};

/**
 * Delete multiple images from Cloudinary
 * @param imageUrls - Array of Cloudinary URLs to delete
 */
export const deleteMultipleImages = async (imageUrls: string[]): Promise<void> => {
  try {
    console.log(`Starting multiple image deletion: ${imageUrls.length} images`);

    const deletePromises = imageUrls.map(url => deleteImage(url));
    await Promise.all(deletePromises);

    console.log('Multiple image deletion completed');
  } catch (error) {
    console.error('Error deleting multiple images:', error);
    throw error;
  }
};

/**
 * Compress an image file before upload
 * @param file - The image file to compress
 * @param maxWidth - Maximum width in pixels
 * @param maxHeight - Maximum height in pixels
 * @param quality - JPEG quality (0-1)
 * @returns Promise<File> - The compressed image file
 */
export const compressImage = async (
  file: File,
  maxWidth: number = 1200,
  maxHeight: number = 1200,
  quality: number = 0.8
): Promise<File> => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img;
      
      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }
      }

      canvas.width = width;
      canvas.height = height;

      // Draw and compress
      ctx?.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now(),
            });
            resolve(compressedFile);
          } else {
            reject(new Error('Failed to compress image'));
          }
        },
        file.type,
        quality
      );
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
};

/**
 * Generate a thumbnail from an image file
 * @param file - The image file
 * @param size - Thumbnail size in pixels (square)
 * @returns Promise<string> - Data URL of the thumbnail
 */
export const generateThumbnail = async (
  file: File,
  size: number = 150
): Promise<string> => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      canvas.width = size;
      canvas.height = size;

      // Calculate crop dimensions for square thumbnail
      const minDimension = Math.min(img.width, img.height);
      const x = (img.width - minDimension) / 2;
      const y = (img.height - minDimension) / 2;

      ctx?.drawImage(
        img,
        x, y, minDimension, minDimension,
        0, 0, size, size
      );

      resolve(canvas.toDataURL());
    };

    img.onerror = () => reject(new Error('Failed to generate thumbnail'));
    img.src = URL.createObjectURL(file);
  });
};

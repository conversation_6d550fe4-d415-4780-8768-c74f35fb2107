import { cloudinaryConfig, validateCloudinaryConfig, getCloudinaryUploadUrl } from '@/lib/cloudinary';

export interface CloudinaryUploadResponse {
  public_id: string;
  secure_url: string;
  url: string;
  width: number;
  height: number;
  format: string;
  resource_type: string;
  bytes: number;
}

export interface CloudinaryUploadError {
  error: {
    message: string;
    http_code: number;
  };
}

export interface UploadOptions {
  folder?: string;
  transformation?: string;
  quality?: string;
  format?: string;
}

class CloudinaryService {
  private uploadUrl: string;

  constructor() {
    if (!validateCloudinaryConfig()) {
      throw new Error('Invalid Cloudinary configuration');
    }
    this.uploadUrl = getCloudinaryUploadUrl();
  }

  /**
   * Upload a single image file to Cloudinary
   */
  async uploadImage(
    file: File,
    options: UploadOptions = {}
  ): Promise<CloudinaryUploadResponse> {
    try {
      const formData = new FormData();

      // Add the file
      formData.append('file', file);

      // Try multiple upload strategies
      let response: Response;

      // Strategy 1: Try with our custom preset
      try {
        formData.append('upload_preset', 'ecopulse_products');
        formData.append('cloud_name', cloudinaryConfig.cloudName);

        // Add optional parameters
        if (options.folder) {
          formData.append('folder', options.folder);
        }

        if (options.quality) {
          formData.append('quality', options.quality);
        }

        if (options.format) {
          formData.append('format', options.format);
        }

        response = await fetch(this.uploadUrl, {
          method: 'POST',
          body: formData,
        });

        if (response.ok) {
          const result: CloudinaryUploadResponse = await response.json();
          return result;
        }
      } catch (error) {
        console.log('Custom preset failed, trying fallback...');
      }

      // Strategy 2: Try with basic unsigned upload (no preset)
      const fallbackFormData = new FormData();
      fallbackFormData.append('file', file);
      fallbackFormData.append('cloud_name', cloudinaryConfig.cloudName);

      // Add timestamp for basic security
      const timestamp = Math.round(Date.now() / 1000);
      fallbackFormData.append('timestamp', timestamp.toString());

      // Add folder if specified
      if (options.folder) {
        fallbackFormData.append('folder', options.folder);
      }

      response = await fetch(this.uploadUrl, {
        method: 'POST',
        body: fallbackFormData,
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Cloudinary upload error:', errorText);
        throw new Error(`Upload failed: ${response.status} - ${errorText}`);
      }

      const result: CloudinaryUploadResponse = await response.json();
      console.log('Cloudinary upload successful:', {
        public_id: result.public_id,
        secure_url: result.secure_url,
        width: result.width,
        height: result.height
      });

      return result;
    } catch (error) {
      console.error('Cloudinary upload error:', error);
      throw error;
    }
  }

  /**
   * Upload multiple images to Cloudinary
   */
  async uploadMultipleImages(
    files: File[],
    options: UploadOptions = {}
  ): Promise<CloudinaryUploadResponse[]> {
    try {
      // Validate all files first
      for (const file of files) {
        const validation = this.validateFile(file);
        if (!validation.isValid) {
          throw new Error(`Invalid file "${file.name}": ${validation.error}`);
        }
      }

      // Upload all files
      const uploadPromises = files.map((file, index) =>
        this.uploadImage(file, options).catch(error => {
          throw new Error(`Failed to upload "${file.name}": ${error.message}`);
        })
      );

      const results = await Promise.all(uploadPromises);
      return results;
    } catch (error) {
      console.error('Multiple upload error:', error);
      throw error;
    }
  }

  /**
   * Delete an image from Cloudinary
   */
  async deleteImage(publicId: string): Promise<void> {
    try {
      // Note: Deletion requires server-side implementation for security
      // For now, we'll just log the deletion request
      console.log('Delete request for public_id:', publicId);
      
      // In a real implementation, this would be handled by your backend
      // which would use the Cloudinary Admin API with the API secret
    } catch (error) {
      console.error('Cloudinary delete error:', error);
      throw error;
    }
  }

  /**
   * Generate optimized image URL with transformations
   */
  getOptimizedUrl(
    publicId: string, 
    transformations: {
      width?: number;
      height?: number;
      quality?: string;
      format?: string;
      crop?: string;
    } = {}
  ): string {
    const { cloudName } = cloudinaryConfig;
    const { width, height, quality = 'auto', format = 'auto', crop = 'fill' } = transformations;
    
    let transformString = `q_${quality},f_${format}`;
    
    if (width && height) {
      transformString += `,w_${width},h_${height},c_${crop}`;
    } else if (width) {
      transformString += `,w_${width}`;
    } else if (height) {
      transformString += `,h_${height}`;
    }
    
    return `https://res.cloudinary.com/${cloudName}/image/upload/${transformString}/${publicId}`;
  }

  /**
   * Validate file before upload
   */
  validateFile(file: File): { isValid: boolean; error?: string } {
    // Check file type
    if (!file.type.startsWith('image/')) {
      return { isValid: false, error: 'File must be an image' };
    }

    // Check file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return { isValid: false, error: 'File size must be less than 10MB' };
    }

    // Check supported formats
    const supportedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!supportedFormats.includes(file.type)) {
      return { isValid: false, error: 'Unsupported file format. Please use JPEG, PNG, GIF, or WebP' };
    }

    return { isValid: true };
  }
}

export const cloudinaryService = new CloudinaryService();

import { cloudinaryConfig, validateCloudinaryConfig, getCloudinaryUploadUrl } from '@/lib/cloudinary';

export interface CloudinaryUploadResponse {
  public_id: string;
  secure_url: string;
  url: string;
  width: number;
  height: number;
  format: string;
  resource_type: string;
  bytes: number;
}

export interface CloudinaryUploadError {
  error: {
    message: string;
    http_code: number;
  };
}

export interface UploadOptions {
  folder?: string;
  transformation?: string;
  quality?: string;
  format?: string;
}

class CloudinaryService {
  private uploadUrl: string;

  constructor() {
    if (!validateCloudinaryConfig()) {
      throw new Error('Invalid Cloudinary configuration');
    }
    this.uploadUrl = getCloudinaryUploadUrl();
  }

  /**
   * Upload a single image file to Cloudinary
   */
  async uploadImage(
    file: File,
    options: UploadOptions = {}
  ): Promise<CloudinaryUploadResponse> {
    try {
      const formData = new FormData();

      // Add the file
      formData.append('file', file);

      // Use a simple approach - try common preset names that might exist
      const presetNames = ['ecopulse_products', 'ml_default', 'default'];
      let response: Response | null = null;
      let lastError: string = '';

      for (const presetName of presetNames) {
        try {
          const attemptFormData = new FormData();
          attemptFormData.append('file', file);
          attemptFormData.append('upload_preset', presetName);
          attemptFormData.append('cloud_name', cloudinaryConfig.cloudName);

          // Add optional parameters
          if (options.folder) {
            attemptFormData.append('folder', options.folder);
          }

          if (options.quality) {
            attemptFormData.append('quality', options.quality);
          }

          if (options.format) {
            attemptFormData.append('format', options.format);
          }

          response = await fetch(this.uploadUrl, {
            method: 'POST',
            body: attemptFormData,
          });

          if (response.ok) {
            console.log(`Upload successful with preset: ${presetName}`);
            break;
          } else {
            const errorText = await response.text();
            lastError = errorText;
            console.log(`Preset ${presetName} failed:`, errorText);
          }
        } catch (error) {
          console.log(`Preset ${presetName} failed with error:`, error);
          lastError = error instanceof Error ? error.message : 'Unknown error';
        }
      }

      if (!response || !response.ok) {
        console.error('All upload presets failed. Last error:', lastError);
        throw new Error(`Upload failed: No valid upload preset found. Please create 'ecopulse_products' preset in your Cloudinary dashboard. Last error: ${lastError}`);
      }

      const result: CloudinaryUploadResponse = await response.json();
      console.log('Cloudinary upload successful:', {
        public_id: result.public_id,
        secure_url: result.secure_url,
        width: result.width,
        height: result.height
      });

      return result;
    } catch (error) {
      console.error('Cloudinary upload error:', error);
      throw error;
    }
  }

  /**
   * Upload multiple images to Cloudinary
   */
  async uploadMultipleImages(
    files: File[],
    options: UploadOptions = {}
  ): Promise<CloudinaryUploadResponse[]> {
    try {
      // Validate all files first
      for (const file of files) {
        const validation = this.validateFile(file);
        if (!validation.isValid) {
          throw new Error(`Invalid file "${file.name}": ${validation.error}`);
        }
      }

      // Upload all files
      const uploadPromises = files.map((file, index) =>
        this.uploadImage(file, options).catch(error => {
          throw new Error(`Failed to upload "${file.name}": ${error.message}`);
        })
      );

      const results = await Promise.all(uploadPromises);
      return results;
    } catch (error) {
      console.error('Multiple upload error:', error);
      throw error;
    }
  }

  /**
   * Create upload preset programmatically
   */
  private async createUploadPreset(): Promise<void> {
    try {
      const presetData = {
        name: 'ecopulse_products',
        unsigned: true,
        folder: 'ecopulse/products',
        allowed_formats: 'jpg,png,gif,webp',
        max_file_size: 10485760, // 10MB
        quality: 'auto',
        format: 'auto'
      };

      // This requires admin API access - for now we'll just log
      console.log('Upload preset creation attempted:', presetData);

      // Note: Creating presets programmatically requires server-side implementation
      // with admin API credentials for security reasons
    } catch (error) {
      console.error('Error creating upload preset:', error);
    }
  }

  /**
   * Delete an image from Cloudinary
   */
  async deleteImage(publicId: string): Promise<void> {
    try {
      // Note: Deletion requires server-side implementation for security
      // For now, we'll just log the deletion request
      console.log('Delete request for public_id:', publicId);

      // In a real implementation, this would be handled by your backend
      // which would use the Cloudinary Admin API with the API secret
    } catch (error) {
      console.error('Cloudinary delete error:', error);
      throw error;
    }
  }

  /**
   * Generate optimized image URL with transformations
   */
  getOptimizedUrl(
    publicId: string, 
    transformations: {
      width?: number;
      height?: number;
      quality?: string;
      format?: string;
      crop?: string;
    } = {}
  ): string {
    const { cloudName } = cloudinaryConfig;
    const { width, height, quality = 'auto', format = 'auto', crop = 'fill' } = transformations;
    
    let transformString = `q_${quality},f_${format}`;
    
    if (width && height) {
      transformString += `,w_${width},h_${height},c_${crop}`;
    } else if (width) {
      transformString += `,w_${width}`;
    } else if (height) {
      transformString += `,h_${height}`;
    }
    
    return `https://res.cloudinary.com/${cloudName}/image/upload/${transformString}/${publicId}`;
  }

  /**
   * Validate file before upload
   */
  validateFile(file: File): { isValid: boolean; error?: string } {
    // Check file type
    if (!file.type.startsWith('image/')) {
      return { isValid: false, error: 'File must be an image' };
    }

    // Check file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return { isValid: false, error: 'File size must be less than 10MB' };
    }

    // Check supported formats
    const supportedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!supportedFormats.includes(file.type)) {
      return { isValid: false, error: 'Unsupported file format. Please use JPEG, PNG, GIF, or WebP' };
    }

    return { isValid: true };
  }
}

export const cloudinaryService = new CloudinaryService();

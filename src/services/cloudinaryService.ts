import { cloudinaryConfig, validateCloudinaryConfig, getCloudinaryUploadUrl } from '@/lib/cloudinary';

export interface CloudinaryUploadResponse {
  public_id: string;
  secure_url: string;
  url: string;
  width: number;
  height: number;
  format: string;
  resource_type: string;
  bytes: number;
}

export interface CloudinaryUploadError {
  error: {
    message: string;
    http_code: number;
  };
}

export interface UploadOptions {
  folder?: string;
  transformation?: string;
  quality?: string;
  format?: string;
}

class CloudinaryService {
  private uploadUrl: string;

  constructor() {
    if (!validateCloudinaryConfig()) {
      throw new Error('Invalid Cloudinary configuration');
    }
    this.uploadUrl = getCloudinaryUploadUrl();
  }

  /**
   * Upload a single image file to Cloudinary - simplest approach
   */
  async uploadImage(
    file: File,
    options: UploadOptions = {}
  ): Promise<CloudinaryUploadResponse> {
    try {
      const formData = new FormData();

      // Just add the file - that's it!
      formData.append('file', file);

      // Add your cloud name
      formData.append('cloud_name', cloudinaryConfig.cloudName);

      // Try upload without any authentication first
      const response = await fetch(this.uploadUrl, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Cloudinary upload error:', errorText);

        // If it fails, it means we need some form of authentication
        // Let's try with just the API key
        const formDataWithKey = new FormData();
        formDataWithKey.append('file', file);
        formDataWithKey.append('api_key', cloudinaryConfig.apiKey);

        const retryResponse = await fetch(this.uploadUrl, {
          method: 'POST',
          body: formDataWithKey,
        });

        if (!retryResponse.ok) {
          const retryErrorText = await retryResponse.text();
          throw new Error(`Upload failed: ${retryResponse.status} - ${retryErrorText}`);
        }

        const result: CloudinaryUploadResponse = await retryResponse.json();
        console.log('Cloudinary upload successful with API key:', result.secure_url);
        return result;
      }

      const result: CloudinaryUploadResponse = await response.json();
      console.log('Cloudinary upload successful (no auth needed):', result.secure_url);

      return result;
    } catch (error) {
      console.error('Cloudinary upload error:', error);
      throw error;
    }
  }

  /**
   * Upload multiple images to Cloudinary
   */
  async uploadMultipleImages(
    files: File[],
    options: UploadOptions = {}
  ): Promise<CloudinaryUploadResponse[]> {
    try {
      // Validate all files first
      for (const file of files) {
        const validation = this.validateFile(file);
        if (!validation.isValid) {
          throw new Error(`Invalid file "${file.name}": ${validation.error}`);
        }
      }

      // Upload all files
      const uploadPromises = files.map((file, index) =>
        this.uploadImage(file, options).catch(error => {
          throw new Error(`Failed to upload "${file.name}": ${error.message}`);
        })
      );

      const results = await Promise.all(uploadPromises);
      return results;
    } catch (error) {
      console.error('Multiple upload error:', error);
      throw error;
    }
  }

  /**
   * Create upload preset programmatically using Admin API
   */
  private async createUploadPreset(): Promise<boolean> {
    try {
      console.log('Creating upload preset programmatically...');

      // Generate signature for admin API call
      const timestamp = Math.round(Date.now() / 1000);
      const params = {
        name: 'ecopulse_products',
        unsigned: true,
        folder: 'ecopulse/products',
        allowed_formats: 'jpg,png,gif,webp',
        max_file_size: 10485760, // 10MB
        quality: 'auto',
        format: 'auto',
        timestamp: timestamp
      };

      // Create signature string for admin API
      const paramString = Object.entries(params)
        .sort(([a], [b]) => a.localeCompare(b))
        .map(([key, value]) => `${key}=${value}`)
        .join('&');

      const stringToSign = paramString + cloudinaryConfig.apiSecret;

      // Simple hash function (in production, use crypto library)
      const signature = await this.generateSignature(stringToSign);

      const adminUrl = `https://api.cloudinary.com/v1_1/${cloudinaryConfig.cloudName}/upload_presets`;

      const formData = new FormData();
      Object.entries(params).forEach(([key, value]) => {
        formData.append(key, value.toString());
      });
      formData.append('api_key', cloudinaryConfig.apiKey);
      formData.append('signature', signature);

      const response = await fetch(adminUrl, {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Upload preset created successfully:', result);
        return true;
      } else {
        const errorText = await response.text();
        console.error('Failed to create upload preset:', errorText);
        return false;
      }
    } catch (error) {
      console.error('Error creating upload preset:', error);
      return false;
    }
  }

  /**
   * Generate signature for Cloudinary upload
   */
  private async generateUploadSignature(timestamp: number, folder: string): Promise<string> {
    try {
      // Create parameters for signature
      const params = {
        folder: folder,
        timestamp: timestamp
      };

      // Create signature string
      const paramString = Object.entries(params)
        .sort(([a], [b]) => a.localeCompare(b))
        .map(([key, value]) => `${key}=${value}`)
        .join('&');

      const stringToSign = paramString + cloudinaryConfig.apiSecret;

      // Use Web Crypto API to generate SHA-1 hash
      const encoder = new TextEncoder();
      const data = encoder.encode(stringToSign);
      const hashBuffer = await crypto.subtle.digest('SHA-1', data);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
      return hashHex;
    } catch (error) {
      console.error('Error generating signature:', error);
      // Fallback: simple hash (not secure, but for testing)
      const fallback = btoa(`${timestamp}${folder}${cloudinaryConfig.apiSecret}`).replace(/[^a-zA-Z0-9]/g, '').substring(0, 40);
      return fallback;
    }
  }

  /**
   * Generate signature for Cloudinary Admin API
   */
  private async generateSignature(stringToSign: string): Promise<string> {
    try {
      // Use Web Crypto API to generate SHA-1 hash
      const encoder = new TextEncoder();
      const data = encoder.encode(stringToSign);
      const hashBuffer = await crypto.subtle.digest('SHA-1', data);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
      return hashHex;
    } catch (error) {
      console.error('Error generating signature:', error);
      // Fallback: simple hash (not secure, but for testing)
      return btoa(stringToSign).replace(/[^a-zA-Z0-9]/g, '').substring(0, 40);
    }
  }

  /**
   * Delete an image from Cloudinary
   */
  async deleteImage(publicId: string): Promise<void> {
    try {
      // Note: Deletion requires server-side implementation for security
      // For now, we'll just log the deletion request
      console.log('Delete request for public_id:', publicId);

      // In a real implementation, this would be handled by your backend
      // which would use the Cloudinary Admin API with the API secret
    } catch (error) {
      console.error('Cloudinary delete error:', error);
      throw error;
    }
  }

  /**
   * Generate optimized image URL with transformations
   */
  getOptimizedUrl(
    publicId: string, 
    transformations: {
      width?: number;
      height?: number;
      quality?: string;
      format?: string;
      crop?: string;
    } = {}
  ): string {
    const { cloudName } = cloudinaryConfig;
    const { width, height, quality = 'auto', format = 'auto', crop = 'fill' } = transformations;
    
    let transformString = `q_${quality},f_${format}`;
    
    if (width && height) {
      transformString += `,w_${width},h_${height},c_${crop}`;
    } else if (width) {
      transformString += `,w_${width}`;
    } else if (height) {
      transformString += `,h_${height}`;
    }
    
    return `https://res.cloudinary.com/${cloudName}/image/upload/${transformString}/${publicId}`;
  }

  /**
   * Validate file before upload
   */
  validateFile(file: File): { isValid: boolean; error?: string } {
    // Check file type
    if (!file.type.startsWith('image/')) {
      return { isValid: false, error: 'File must be an image' };
    }

    // Check file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return { isValid: false, error: 'File size must be less than 10MB' };
    }

    // Check supported formats
    const supportedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!supportedFormats.includes(file.type)) {
      return { isValid: false, error: 'Unsupported file format. Please use JPEG, PNG, GIF, or WebP' };
    }

    return { isValid: true };
  }
}

export const cloudinaryService = new CloudinaryService();

import {
  productsDB,
  DatabaseProduct,
  convertTimestamp
} from '@/lib/database';
import { Product } from '@/types/product';

// Convert Database product to app product type
const convertDatabaseProduct = (dbProduct: DatabaseProduct): Product => {
  // Handle both old single image format and new multiple images format
  let images: string[] = [];
  if (dbProduct.images && dbProduct.images.length > 0) {
    // Use new images array if available
    images = dbProduct.images;
  } else if (dbProduct.image) {
    // Fall back to single image for backward compatibility
    images = [dbProduct.image];
  }

  return {
    id: dbProduct.id,
    name: dbProduct.name,
    description: dbProduct.description,
    price: dbProduct.price,
    category: dbProduct.category,
    categoryId: dbProduct.categoryId,
    stock: dbProduct.stock,
    minStock: 0, // Default value
    sku: `SKU-${dbProduct.id}`, // Generate SKU from ID
    images: images,
    status: dbProduct.status as 'active' | 'inactive' | 'draft',
    createdAt: convertTimestamp(dbProduct.createdAt).toISOString(),
    updatedAt: convertTimestamp(dbProduct.updatedAt).toISOString()
  };
};

// Convert app product to Database product type
const convertToDatabaseProduct = (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Omit<DatabaseProduct, 'id' | 'createdAt' | 'updatedAt'> => {
  return {
    name: product.name,
    description: product.description,
    price: product.price,
    category: product.category,
    categoryId: product.categoryId,
    stock: product.stock,
    image: product.images?.[0] || '', // Keep first image for backward compatibility
    images: product.images || [], // Store all images in new field
    status: product.status === 'draft' ? 'inactive' : product.status as 'active' | 'inactive'
  };
};

export const productService = {
  // Get all products
  async getAllProducts(): Promise<Product[]> {
    try {
      const dbProducts = await productsDB.getAll();
      return dbProducts.map(convertDatabaseProduct);
    } catch (error) {
      console.error('Error fetching products:', error);
      throw new Error('Failed to fetch products');
    }
  },

  // Get product by ID
  async getProductById(id: string): Promise<Product | null> {
    try {
      const dbProduct = await productsDB.getById(id);
      return dbProduct ? convertDatabaseProduct(dbProduct) : null;
    } catch (error) {
      console.error('Error fetching product:', error);
      throw new Error('Failed to fetch product');
    }
  },

  // Create new product
  async createProduct(productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const dbData = convertToDatabaseProduct(productData);
      return await productsDB.create(dbData);
    } catch (error) {
      console.error('Error creating product:', error);
      throw new Error('Failed to create product');
    }
  },

  // Update product
  async updateProduct(id: string, productData: Partial<Omit<Product, 'id' | 'createdAt' | 'updatedAt'>>): Promise<void> {
    try {
      // Convert the product data to database format
      const dbUpdateData: Partial<Omit<DatabaseProduct, 'id' | 'createdAt'>> = {};

      if (productData.name) dbUpdateData.name = productData.name;
      if (productData.description) dbUpdateData.description = productData.description;
      if (productData.price) dbUpdateData.price = productData.price;
      if (productData.category) dbUpdateData.category = productData.category;
      if (productData.categoryId) dbUpdateData.categoryId = productData.categoryId;
      if (productData.stock !== undefined) dbUpdateData.stock = productData.stock;
      if (productData.images) {
        dbUpdateData.image = productData.images[0] || ''; // Keep first image for backward compatibility
        dbUpdateData.images = productData.images; // Store all images
      }
      if (productData.status) {
        dbUpdateData.status = productData.status === 'draft' ? 'inactive' : productData.status as 'active' | 'inactive';
      }

      await productsDB.update(id, dbUpdateData);
    } catch (error) {
      console.error('Error updating product:', error);
      throw new Error('Failed to update product');
    }
  },

  // Delete product
  async deleteProduct(id: string): Promise<void> {
    try {
      await productsDB.delete(id);
    } catch (error) {
      console.error('Error deleting product:', error);
      throw new Error('Failed to delete product');
    }
  },

  // Get products by category
  async getProductsByCategory(categoryId: string): Promise<Product[]> {
    try {
      const dbProducts = await productsDB.queryByField('categoryId', categoryId);
      return dbProducts.map(convertDatabaseProduct);
    } catch (error) {
      console.error('Error fetching products by category:', error);
      throw new Error('Failed to fetch products by category');
    }
  },

  // Get products by status
  async getProductsByStatus(status: 'active' | 'inactive'): Promise<Product[]> {
    try {
      const dbProducts = await productsDB.queryByField('status', status);
      return dbProducts.map(convertDatabaseProduct);
    } catch (error) {
      console.error('Error fetching products by status:', error);
      throw new Error('Failed to fetch products by status');
    }
  },

  // Search products by name
  async searchProducts(searchTerm: string): Promise<Product[]> {
    try {
      // Note: Firebase Realtime Database doesn't support full-text search natively
      // This is a simple implementation that gets all products and filters client-side
      // For production, consider using Algolia or similar service
      const allProducts = await this.getAllProducts();
      return allProducts.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    } catch (error) {
      console.error('Error searching products:', error);
      throw new Error('Failed to search products');
    }
  },

  // Get low stock products
  async getLowStockProducts(threshold: number = 10): Promise<Product[]> {
    try {
      // Firebase Realtime Database doesn't support complex queries like Firestore
      // Get all products and filter client-side
      const allProducts = await this.getAllProducts();
      return allProducts.filter(product =>
        product.stock <= threshold && product.status === 'active'
      );
    } catch (error) {
      console.error('Error fetching low stock products:', error);
      throw new Error('Failed to fetch low stock products');
    }
  }
};

'use client';

import React from 'react';
import { Modal } from '@/components/ui/Modal';
import { Badge } from '@/components/ui/Badge';
import { formatCurrency, formatDate } from '@/lib/utils';
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  DollarSign,
  ShoppingCart,
  Clock
} from 'lucide-react';

interface Customer {
  id: number;
  name: string;
  email: string;
  phone: string;
  address: string;
  joinDate: string;
  totalOrders: number;
  totalSpent: number;
  status: 'active' | 'inactive' | 'vip' | 'new';
  lastOrder: string;
}

interface CustomerDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  customer: Customer | null;
}

const getStatusVariant = (status: Customer['status']) => {
  switch (status) {
    case 'vip':
      return 'default';
    case 'active':
      return 'success';
    case 'new':
      return 'secondary';
    case 'inactive':
      return 'warning';
    default:
      return 'secondary';
  }
};

export const CustomerDetailModal: React.FC<CustomerDetailModalProps> = ({
  isOpen,
  onClose,
  customer,
}) => {
  if (!customer) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={customer.name}
      description="Complete customer information and order history"
      size="lg"
    >
      <div className="space-y-6">
        {/* Customer Status */}
        <div className="flex items-center justify-between p-4 bg-neutral-50 rounded-lg">
          <div className="flex items-center space-x-4">
            <div className="h-16 w-16 bg-primary-100 rounded-full flex items-center justify-center">
              <User className="h-8 w-8 text-primary-600" />
            </div>
            <div>
              <h3 className="text-xl font-semibold text-neutral-900">{customer.name}</h3>
              <div className="flex items-center space-x-2 mt-1">
                <Badge variant={getStatusVariant(customer.status)} size="sm">
                  {customer.status.toUpperCase()}
                </Badge>
                <span className="text-sm text-neutral-600">Customer ID: {customer.id}</span>
              </div>
            </div>
          </div>
          <div className="text-right">
            <p className="text-sm text-neutral-600">Total Spent</p>
            <p className="text-2xl font-bold text-neutral-900">{formatCurrency(customer.totalSpent)}</p>
          </div>
        </div>

        {/* Contact Information */}
        <div className="space-y-4">
          <h4 className="text-lg font-medium text-neutral-900 flex items-center">
            <Mail className="h-5 w-5 mr-2" />
            Contact Information
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-neutral-50 rounded-lg">
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Mail className="h-4 w-4 text-neutral-400" />
                <div>
                  <p className="text-sm text-neutral-600">Email</p>
                  <p className="font-medium text-neutral-900">{customer.email}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Phone className="h-4 w-4 text-neutral-400" />
                <div>
                  <p className="text-sm text-neutral-600">Phone</p>
                  <p className="font-medium text-neutral-900">{customer.phone}</p>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <MapPin className="h-4 w-4 text-neutral-400 mt-1" />
                <div>
                  <p className="text-sm text-neutral-600">Address</p>
                  <p className="font-medium text-neutral-900">{customer.address}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Order Statistics */}
        <div className="space-y-4">
          <h4 className="text-lg font-medium text-neutral-900 flex items-center">
            <ShoppingCart className="h-5 w-5 mr-2" />
            Order Statistics
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <ShoppingCart className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-blue-600">Total Orders</p>
                  <p className="text-xl font-bold text-blue-900">{customer.totalOrders}</p>
                </div>
              </div>
            </div>

            <div className="p-4 bg-green-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="h-10 w-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <DollarSign className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-green-600">Total Spent</p>
                  <p className="text-xl font-bold text-green-900">{formatCurrency(customer.totalSpent)}</p>
                </div>
              </div>
            </div>

            <div className="p-4 bg-purple-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="h-10 w-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <DollarSign className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-purple-600">Average Order</p>
                  <p className="text-xl font-bold text-purple-900">
                    {customer.totalOrders > 0 
                      ? formatCurrency(customer.totalSpent / customer.totalOrders)
                      : formatCurrency(0)
                    }
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Account Information */}
        <div className="space-y-4">
          <h4 className="text-lg font-medium text-neutral-900 flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            Account Information
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Calendar className="h-4 w-4 text-neutral-400" />
                <div>
                  <p className="text-sm text-neutral-600">Join Date</p>
                  <p className="font-medium text-neutral-900">{formatDate(customer.joinDate)}</p>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Clock className="h-4 w-4 text-neutral-400" />
                <div>
                  <p className="text-sm text-neutral-600">Last Order</p>
                  <p className="font-medium text-neutral-900">
                    {customer.lastOrder ? formatDate(customer.lastOrder) : 'No orders yet'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Customer Insights */}
        <div className="p-4 bg-neutral-50 rounded-lg">
          <h5 className="font-medium text-neutral-900 mb-2">Customer Insights</h5>
          <div className="space-y-2 text-sm text-neutral-600">
            <p>
              • Customer since {formatDate(customer.joinDate)} 
              ({Math.ceil((new Date().getTime() - new Date(customer.joinDate).getTime()) / (1000 * 60 * 60 * 24))} days)
            </p>
            <p>
              • {customer.status === 'vip' ? 'VIP customer with high value orders' : 
                 customer.status === 'active' ? 'Active customer with regular purchases' :
                 customer.status === 'new' ? 'New customer, recently joined' :
                 'Inactive customer, may need re-engagement'}
            </p>
            {customer.totalOrders > 0 && (
              <p>
                • Average order value: {formatCurrency(customer.totalSpent / customer.totalOrders)}
              </p>
            )}
          </div>
        </div>
      </div>
    </Modal>
  );
};

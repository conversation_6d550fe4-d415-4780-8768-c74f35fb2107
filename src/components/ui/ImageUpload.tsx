'use client';

import React, { useState, useRef } from 'react';
import { Button } from './Button';
import { Label } from './Label';
import { Upload, X, Image as ImageIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ImageUploadProps {
  label?: string;
  value?: string[];
  onChange: (images: string[]) => void;
  maxImages?: number;
  error?: string;
  helperText?: string;
  required?: boolean;
  accept?: string;
}

export const ImageUpload: React.FC<ImageUploadProps> = ({
  label,
  value = [],
  onChange,
  maxImages = 5,
  error,
  helperText,
  required = false,
  accept = 'image/*',
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (files: FileList | null) => {
    if (!files) return;

    const newImages: string[] = [];
    const remainingSlots = maxImages - value.length;
    const filesToProcess = Math.min(files.length, remainingSlots);

    for (let i = 0; i < filesToProcess; i++) {
      const file = files[i];
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const result = e.target?.result as string;
          newImages.push(result);
          
          if (newImages.length === filesToProcess) {
            onChange([...value, ...newImages]);
          }
        };
        reader.readAsDataURL(file);
      }
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const removeImage = (index: number) => {
    const newImages = value.filter((_, i) => i !== index);
    onChange(newImages);
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  const canAddMore = value.length < maxImages;

  return (
    <div className="space-y-2">
      {label && (
        <Label required={required}>
          {label}
        </Label>
      )}
      
      <div className="space-y-4">
        {/* Upload Area */}
        {canAddMore && (
          <div
            className={cn(
              'border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors',
              isDragging
                ? 'border-primary-500 bg-primary-50'
                : 'border-neutral-300 hover:border-neutral-400',
              error && 'border-error-500'
            )}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onClick={openFileDialog}
          >
            <Upload className="h-8 w-8 text-neutral-400 mx-auto mb-2" />
            <p className="text-sm text-neutral-600 mb-1">
              Click to upload or drag and drop
            </p>
            <p className="text-xs text-neutral-500">
              PNG, JPG, GIF up to 10MB ({value.length}/{maxImages} images)
            </p>
          </div>
        )}

        {/* Image Preview Grid */}
        {value.length > 0 && (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {value.map((image, index) => (
              <div key={index} className="relative group">
                <div className="aspect-square rounded-lg overflow-hidden bg-neutral-100 border-2 border-neutral-200">
                  <img
                    src={image}
                    alt={`Upload ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </div>
                <button
                  type="button"
                  onClick={() => removeImage(index)}
                  className="absolute -top-2 -right-2 bg-error-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <X className="h-3 w-3" />
                </button>
                {index === 0 && (
                  <div className="absolute bottom-2 left-2 bg-primary-500 text-white text-xs px-2 py-1 rounded">
                    Primary
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Empty State */}
        {value.length === 0 && (
          <div className="text-center py-8 border-2 border-dashed border-neutral-200 rounded-lg">
            <ImageIcon className="h-12 w-12 text-neutral-400 mx-auto mb-2" />
            <p className="text-sm text-neutral-500">No images uploaded yet</p>
          </div>
        )}
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        multiple
        className="hidden"
        onChange={(e) => handleFileSelect(e.target.files)}
      />

      {/* Error Message */}
      {error && (
        <p className="text-sm text-error-600">{error}</p>
      )}

      {/* Helper Text */}
      {helperText && !error && (
        <p className="text-sm text-neutral-500">{helperText}</p>
      )}
    </div>
  );
};

'use client';

import React, { useState, useRef } from 'react';
import { Button } from './Button';
import { Label } from './Label';
import { Upload, X, Image as ImageIcon, Loader2, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { uploadMultipleImages, deleteImage } from '@/lib/imageUpload';

interface ImageUploadProps {
  label?: string;
  value?: string[];
  onChange: (images: string[]) => void;
  maxImages?: number;
  error?: string;
  helperText?: string;
  required?: boolean;
  accept?: string;
  storagePath?: string; // Firebase Storage path
}

export const ImageUpload: React.FC<ImageUploadProps> = ({
  label,
  value = [],
  onChange,
  maxImages = 5,
  error,
  helperText,
  required = false,
  accept = 'image/*',
  storagePath = 'products',
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (files: FileList | null) => {
    if (!files || isUploading) return;

    const remainingSlots = maxImages - value.length;
    const filesToProcess = Math.min(files.length, remainingSlots);

    if (filesToProcess === 0) return;

    const fileArray = Array.from(files).slice(0, filesToProcess);
    const validFiles = fileArray.filter(file => file.type.startsWith('image/'));

    if (validFiles.length === 0) return;

    setIsUploading(true);
    setUploadError(null);

    try {
      const uploadedUrls = await uploadMultipleImages(validFiles, storagePath);
      onChange([...value, ...uploadedUrls]);
      setUploadError(null);
    } catch (error) {
      console.error('Error uploading images:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to upload images. Please try again.';
      setUploadError(errorMessage);
    } finally {
      setIsUploading(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const removeImage = async (index: number) => {
    const imageToRemove = value[index];
    const newImages = value.filter((_, i) => i !== index);
    onChange(newImages);

    // Delete from Cloudinary if it's a Cloudinary URL
    if (imageToRemove && imageToRemove.includes('res.cloudinary.com')) {
      try {
        await deleteImage(imageToRemove);
      } catch (error) {
        console.error('Error deleting image from Cloudinary:', error);
        // Image removed from UI but not from storage - this is acceptable
      }
    }
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  const canAddMore = value.length < maxImages;

  return (
    <div className="space-y-2">
      {label && (
        <Label required={required}>
          {label}
        </Label>
      )}
      
      <div className="space-y-4">
        {/* Upload Area */}
        {canAddMore && (
          <div
            className={cn(
              'border-2 border-dashed rounded-lg p-6 text-center transition-colors',
              isUploading ? 'cursor-not-allowed opacity-50' : 'cursor-pointer',
              isDragging
                ? 'border-primary-500 bg-primary-50'
                : 'border-neutral-300 hover:border-neutral-400',
              error && 'border-error-500'
            )}
            onDrop={isUploading ? undefined : handleDrop}
            onDragOver={isUploading ? undefined : handleDragOver}
            onDragLeave={isUploading ? undefined : handleDragLeave}
            onClick={isUploading ? undefined : openFileDialog}
          >
            {isUploading ? (
              <>
                <Loader2 className="h-8 w-8 text-primary-500 mx-auto mb-2 animate-spin" />
                <p className="text-sm text-primary-600 mb-1">
                  Uploading images...
                </p>
                <p className="text-xs text-neutral-500">
                  Please wait while your images are being uploaded
                </p>
              </>
            ) : (
              <>
                <Upload className="h-8 w-8 text-neutral-400 mx-auto mb-2" />
                <p className="text-sm text-neutral-600 mb-1">
                  Click to upload or drag and drop
                </p>
                <p className="text-xs text-neutral-500">
                  PNG, JPG, GIF up to 10MB ({value.length}/{maxImages} images)
                </p>
              </>
            )}
          </div>
        )}

        {/* Image Preview Grid */}
        {value.length > 0 && (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {value.map((image, index) => (
              <div key={index} className="relative group">
                <div className="aspect-square rounded-lg overflow-hidden bg-neutral-100 border-2 border-neutral-200">
                  <img
                    src={image}
                    alt={`Upload ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </div>
                <button
                  type="button"
                  onClick={() => removeImage(index)}
                  className="absolute -top-2 -right-2 bg-error-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <X className="h-3 w-3" />
                </button>
                {index === 0 && (
                  <div className="absolute bottom-2 left-2 bg-primary-500 text-white text-xs px-2 py-1 rounded">
                    Primary
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Empty State */}
        {value.length === 0 && (
          <div className="text-center py-8 border-2 border-dashed border-neutral-200 rounded-lg">
            <ImageIcon className="h-12 w-12 text-neutral-400 mx-auto mb-2" />
            <p className="text-sm text-neutral-500">No images uploaded yet</p>
          </div>
        )}
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        multiple
        className="hidden"
        onChange={(e) => handleFileSelect(e.target.files)}
      />

      {/* Error Messages */}
      {error && (
        <p className="text-sm text-error-600">{error}</p>
      )}

      {uploadError && (
        <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md">
          <AlertCircle className="h-4 w-4 text-red-600 flex-shrink-0" />
          <p className="text-sm text-red-700">{uploadError}</p>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setUploadError(null)}
            className="ml-auto p-1 h-auto text-red-600 hover:text-red-700"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      )}

      {/* Helper Text */}
      {helperText && !error && !uploadError && (
        <p className="text-sm text-neutral-500">{helperText}</p>
      )}
    </div>
  );
};

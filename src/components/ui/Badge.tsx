import React from 'react';
import { cn } from '@/lib/utils';

// Badge variant styles without class-variance-authority
const badgeVariants = {
  variant: {
    default: 'border-transparent bg-blue-600 text-white hover:bg-blue-700',
    secondary: 'border-transparent bg-gray-100 text-gray-900 hover:bg-gray-200',
    destructive: 'border-transparent bg-red-600 text-white hover:bg-red-700',
    outline: 'text-gray-900 border-gray-300',
    success: 'border-transparent bg-green-600 text-white hover:bg-green-700',
    warning: 'border-transparent bg-yellow-600 text-white hover:bg-yellow-700',
  },
  size: {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-0.5 text-xs',
    lg: 'px-3 py-1 text-sm',
  },
};

const baseBadgeClasses = 'inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2';

export interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: keyof typeof badgeVariants.variant;
  size?: keyof typeof badgeVariants.size;
  icon?: React.ReactNode;
}

function Badge({ className, variant = 'default', size = 'md', icon, children, ...props }: BadgeProps) {
  const variantClasses = badgeVariants.variant[variant];
  const sizeClasses = badgeVariants.size[size];

  return (
    <div
      className={cn(
        baseBadgeClasses,
        variantClasses,
        sizeClasses,
        className
      )}
      {...props}
    >
      {icon && <span className="mr-1">{icon}</span>}
      {children}
    </div>
  );
}

export { Badge };

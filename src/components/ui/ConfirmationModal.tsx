'use client';

import React from 'react';
import { Modal } from './Modal';
import { Button } from './Button';
import { AlertTriangle } from 'lucide-react';

interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'danger' | 'warning' | 'info';
  isLoading?: boolean;
}

export const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  variant = 'danger',
  isLoading = false,
}) => {
  const getVariantStyles = () => {
    switch (variant) {
      case 'danger':
        return {
          iconColor: 'text-error-600',
          iconBg: 'bg-error-100',
          confirmVariant: 'destructive' as const,
        };
      case 'warning':
        return {
          iconColor: 'text-warning-600',
          iconBg: 'bg-warning-100',
          confirmVariant: 'warning' as const,
        };
      case 'info':
        return {
          iconColor: 'text-primary-600',
          iconBg: 'bg-primary-100',
          confirmVariant: 'default' as const,
        };
      default:
        return {
          iconColor: 'text-error-600',
          iconBg: 'bg-error-100',
          confirmVariant: 'destructive' as const,
        };
    }
  };

  const styles = getVariantStyles();

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="sm"
      showCloseButton={false}
    >
      <div className="text-center">
        <div className={`mx-auto flex items-center justify-center h-12 w-12 rounded-full ${styles.iconBg} mb-4`}>
          <AlertTriangle className={`h-6 w-6 ${styles.iconColor}`} />
        </div>
        
        <h3 className="text-lg font-medium text-neutral-900 mb-2">
          {title}
        </h3>
        
        <p className="text-sm text-neutral-600 mb-6">
          {message}
        </p>
        
        <div className="flex justify-center space-x-4">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
          >
            {cancelText}
          </Button>
          <Button
            variant={styles.confirmVariant}
            onClick={onConfirm}
            loading={isLoading}
            disabled={isLoading}
          >
            {confirmText}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

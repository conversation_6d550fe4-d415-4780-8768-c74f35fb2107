'use client';

import React from 'react';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

export type ToastType = 'success' | 'error' | 'warning' | 'info';

export interface Toast {
  type: ToastType;
  title: string;
  message?: string;
}

// Custom hook that wraps react-toastify
export const useToast = () => {
  const addToast = ({ type, title, message }: Toast) => {
    const content = message ? `${title}: ${message}` : title;

    switch (type) {
      case 'success':
        toast.success(content);
        break;
      case 'error':
        toast.error(content);
        break;
      case 'warning':
        toast.warn(content);
        break;
      case 'info':
        toast.info(content);
        break;
      default:
        toast(content);
    }
  };

  return { addToast };
};

// Simple ToastProvider that just renders react-toastify container
export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <>
      {children}
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
        className="mt-16"
      />
    </>
  );
};

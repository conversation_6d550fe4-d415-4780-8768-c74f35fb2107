'use client';

import React from 'react';
import { Modal } from '@/components/ui/Modal';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { Order } from '@/types/order';
import { formatCurrency, formatDate } from '@/lib/utils';
import { 
  User, 
  Phone, 
  Mail, 
  MapPin, 
  Package, 
  CreditCard, 
  Calendar,
  MessageCircle,
  Truck,
  FileText
} from 'lucide-react';

interface OrderDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  order: Order | null;
}

const getOrderStatusVariant = (status: Order['status']) => {
  switch (status) {
    case 'pending': return 'secondary';
    case 'confirmed': return 'default';
    case 'processing': return 'warning';
    case 'shipped': return 'default';
    case 'delivered': return 'success';
    case 'cancelled': return 'destructive';
    default: return 'secondary';
  }
};

const getPaymentStatusVariant = (status: Order['paymentStatus']) => {
  switch (status) {
    case 'pending': return 'warning';
    case 'paid': return 'success';
    case 'failed': return 'destructive';
    case 'refunded': return 'secondary';
    default: return 'secondary';
  }
};

export const OrderDetailModal: React.FC<OrderDetailModalProps> = ({
  isOpen,
  onClose,
  order,
}) => {
  if (!order) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`Order ${order.orderNumber}`}
      description="Complete order details and customer information"
      size="xl"
    >
      <div className="space-y-6">
        {/* Order Status */}
        <div className="flex items-center justify-between p-4 bg-neutral-50 rounded-lg">
          <div className="flex items-center space-x-4">
            <Badge variant={getOrderStatusVariant(order.status)} size="lg">
              {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
            </Badge>
            <Badge variant={getPaymentStatusVariant(order.paymentStatus)} size="lg">
              Payment: {order.paymentStatus.charAt(0).toUpperCase() + order.paymentStatus.slice(1)}
            </Badge>
          </div>
          <div className="text-right">
            <p className="text-sm text-neutral-600">Order Total</p>
            <p className="text-2xl font-bold text-neutral-900">{formatCurrency(order.total)}</p>
          </div>
        </div>

        {/* Customer Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-neutral-900 flex items-center">
            <User className="h-5 w-5 mr-2" />
            Customer Information
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-neutral-50 rounded-lg">
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <User className="h-4 w-4 text-neutral-400" />
                <div>
                  <p className="text-sm text-neutral-600">Name</p>
                  <p className="font-medium">{order.customer.name}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Mail className="h-4 w-4 text-neutral-400" />
                <div>
                  <p className="text-sm text-neutral-600">Email</p>
                  <p className="font-medium">{order.customer.email}</p>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Phone className="h-4 w-4 text-neutral-400" />
                <div>
                  <p className="text-sm text-neutral-600">Phone</p>
                  <p className="font-medium">{order.customer.phone}</p>
                </div>
              </div>

              {order.customer.whatsappNumber && (
                <div className="flex items-center space-x-3">
                  <MessageCircle className="h-4 w-4 text-neutral-400" />
                  <div>
                    <p className="text-sm text-neutral-600">WhatsApp</p>
                    <p className="font-medium">{order.customer.whatsappNumber}</p>
                  </div>
                </div>
              )}
            </div>

            <div className="md:col-span-2">
              <div className="flex items-start space-x-3">
                <MapPin className="h-4 w-4 text-neutral-400 mt-1" />
                <div>
                  <p className="text-sm text-neutral-600">Address</p>
                  <p className="font-medium">{order.customer.address}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Order Items */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-neutral-900 flex items-center">
            <Package className="h-5 w-5 mr-2" />
            Order Items
          </h3>
          
          <div className="space-y-3">
            {order.items.map((item) => (
              <div key={item.id} className="flex items-center justify-between p-4 border border-neutral-200 rounded-lg">
                <div className="flex items-center space-x-4">
                  {item.productImage && (
                    <div className="w-12 h-12 rounded-lg overflow-hidden bg-neutral-100">
                      <img
                        src={item.productImage}
                        alt={item.productName}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}
                  <div>
                    <p className="font-medium text-neutral-900">{item.productName}</p>
                    <p className="text-sm text-neutral-600">SKU: {item.sku}</p>
                    <p className="text-sm text-neutral-600">
                      {formatCurrency(item.unitPrice)} × {item.quantity}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-medium">{formatCurrency(item.totalPrice)}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Order Summary */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-neutral-900 flex items-center">
            <CreditCard className="h-5 w-5 mr-2" />
            Order Summary
          </h3>
          
          <div className="p-4 bg-neutral-50 rounded-lg space-y-2">
            <div className="flex justify-between text-sm">
              <span>Subtotal:</span>
              <span>{formatCurrency(order.subtotal)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Shipping:</span>
              <span>{formatCurrency(order.shipping)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Tax:</span>
              <span>{formatCurrency(order.tax)}</span>
            </div>
            {order.discount > 0 && (
              <div className="flex justify-between text-sm">
                <span>Discount:</span>
                <span>-{formatCurrency(order.discount)}</span>
              </div>
            )}
            <div className="flex justify-between text-lg font-bold border-t pt-2">
              <span>Total:</span>
              <span>{formatCurrency(order.total)}</span>
            </div>
          </div>
        </div>

        {/* Order Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <CreditCard className="h-4 w-4 text-neutral-400" />
              <div>
                <p className="text-sm text-neutral-600">Payment Method</p>
                <p className="font-medium capitalize">{order.paymentMethod}</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <Calendar className="h-4 w-4 text-neutral-400" />
              <div>
                <p className="text-sm text-neutral-600">Order Date</p>
                <p className="font-medium">{formatDate(order.createdAt)}</p>
              </div>
            </div>

            {order.deliveryDate && (
              <div className="flex items-center space-x-3">
                <Truck className="h-4 w-4 text-neutral-400" />
                <div>
                  <p className="text-sm text-neutral-600">Delivery Date</p>
                  <p className="font-medium">{formatDate(order.deliveryDate)}</p>
                </div>
              </div>
            )}
          </div>

          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <Package className="h-4 w-4 text-neutral-400" />
              <div>
                <p className="text-sm text-neutral-600">Order Source</p>
                <p className="font-medium capitalize">{order.source}</p>
              </div>
            </div>

            {order.notes && (
              <div className="flex items-start space-x-3">
                <FileText className="h-4 w-4 text-neutral-400 mt-1" />
                <div>
                  <p className="text-sm text-neutral-600">Notes</p>
                  <p className="font-medium">{order.notes}</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* WhatsApp Link */}
        {order.source === 'whatsapp' && order.whatsappLink && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <MessageCircle className="h-5 w-5 text-green-600" />
                <div>
                  <p className="font-medium text-green-900">WhatsApp Order</p>
                  <p className="text-sm text-green-700">This order was received via WhatsApp</p>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open(order.whatsappLink, '_blank')}
              >
                <MessageCircle className="h-4 w-4 mr-2" />
                Open Chat
              </Button>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

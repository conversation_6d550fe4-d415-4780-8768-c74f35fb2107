'use client';

import React from 'react';
import { Modal } from '@/components/ui/Modal';
import { Badge } from '@/components/ui/Badge';
import { Product } from '@/types/product';
import { formatCurrency, getStockStatus } from '@/lib/utils';
import { 
  Package, 
  Tag, 
  Truck, 
  Calendar, 
  Ruler, 
  Weight,
  Shield,
  Building,
  Barcode
} from 'lucide-react';

interface ProductDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: Product | null;
}

export const ProductDetailModal: React.FC<ProductDetailModalProps> = ({
  isOpen,
  onClose,
  product,
}) => {
  if (!product) return null;

  const stockStatus = getStockStatus(product.stock, product.minStock);

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={product.name}
      description="Product details and specifications"
      size="lg"
    >
      <div className="space-y-6">
        {/* Product Images */}
        {product.images && product.images.length > 0 && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-neutral-900">Images</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {product.images.map((image, index) => (
                <div key={index} className="aspect-square rounded-lg overflow-hidden bg-neutral-100">
                  <img
                    src={image}
                    alt={`${product.name} ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-neutral-900">Basic Information</h3>
            
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Package className="h-4 w-4 text-neutral-400" />
                <div>
                  <p className="text-sm text-neutral-600">Category</p>
                  <p className="font-medium">{product.category}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Tag className="h-4 w-4 text-neutral-400" />
                <div>
                  <p className="text-sm text-neutral-600">SKU</p>
                  <p className="font-mono text-sm">{product.sku}</p>
                </div>
              </div>

              {product.barcode && (
                <div className="flex items-center space-x-3">
                  <Barcode className="h-4 w-4 text-neutral-400" />
                  <div>
                    <p className="text-sm text-neutral-600">Barcode</p>
                    <p className="font-mono text-sm">{product.barcode}</p>
                  </div>
                </div>
              )}

              <div className="flex items-center space-x-3">
                <div className="h-4 w-4 flex items-center justify-center">
                  <div className={`h-2 w-2 rounded-full ${
                    product.status === 'active' ? 'bg-green-500' : 
                    product.status === 'draft' ? 'bg-yellow-500' : 'bg-red-500'
                  }`} />
                </div>
                <div>
                  <p className="text-sm text-neutral-600">Status</p>
                  <Badge 
                    variant={product.status === 'active' ? 'default' : product.status === 'draft' ? 'secondary' : 'secondary'}
                    size="sm"
                  >
                    {product.status === 'active' ? 'Active' : product.status === 'draft' ? 'Draft' : 'Inactive'}
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-medium text-neutral-900">Pricing & Inventory</h3>
            
            <div className="space-y-3">
              <div>
                <p className="text-sm text-neutral-600">Selling Price</p>
                <p className="text-2xl font-bold text-neutral-900">{formatCurrency(product.price)}</p>
              </div>

              {product.costPrice && (
                <div>
                  <p className="text-sm text-neutral-600">Cost Price</p>
                  <p className="font-medium">{formatCurrency(product.costPrice)}</p>
                </div>
              )}

              <div>
                <p className="text-sm text-neutral-600">Stock Level</p>
                <div className="flex items-center space-x-2">
                  <p className="font-medium">{product.stock} units</p>
                  <Badge variant={stockStatus.variant} size="sm">
                    {stockStatus.label}
                  </Badge>
                </div>
              </div>

              <div>
                <p className="text-sm text-neutral-600">Minimum Stock</p>
                <p className="font-medium">{product.minStock} units</p>
              </div>
            </div>
          </div>
        </div>

        {/* Description */}
        <div className="space-y-2">
          <h3 className="text-lg font-medium text-neutral-900">Description</h3>
          <p className="text-neutral-600">{product.description}</p>
        </div>

        {/* Product Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {(product.brand || product.model) && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-neutral-900">Brand & Model</h3>
              
              <div className="space-y-3">
                {product.brand && (
                  <div className="flex items-center space-x-3">
                    <Building className="h-4 w-4 text-neutral-400" />
                    <div>
                      <p className="text-sm text-neutral-600">Brand</p>
                      <p className="font-medium">{product.brand}</p>
                    </div>
                  </div>
                )}

                {product.model && (
                  <div className="flex items-center space-x-3">
                    <Tag className="h-4 w-4 text-neutral-400" />
                    <div>
                      <p className="text-sm text-neutral-600">Model</p>
                      <p className="font-medium">{product.model}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          <div className="space-y-4">
            <h3 className="text-lg font-medium text-neutral-900">Physical Properties</h3>
            
            <div className="space-y-3">
              {product.weight && product.weight > 0 && (
                <div className="flex items-center space-x-3">
                  <Weight className="h-4 w-4 text-neutral-400" />
                  <div>
                    <p className="text-sm text-neutral-600">Weight</p>
                    <p className="font-medium">{product.weight} kg</p>
                  </div>
                </div>
              )}

              {product.dimensions && (
                <div className="flex items-center space-x-3">
                  <Ruler className="h-4 w-4 text-neutral-400" />
                  <div>
                    <p className="text-sm text-neutral-600">Dimensions (L×W×H)</p>
                    <p className="font-medium">
                      {product.dimensions.length} × {product.dimensions.width} × {product.dimensions.height} cm
                    </p>
                  </div>
                </div>
              )}

              {product.warranty && (
                <div className="flex items-center space-x-3">
                  <Shield className="h-4 w-4 text-neutral-400" />
                  <div>
                    <p className="text-sm text-neutral-600">Warranty</p>
                    <p className="font-medium">{product.warranty}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Timestamps */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pt-4 border-t border-neutral-200">
          <div className="flex items-center space-x-3">
            <Calendar className="h-4 w-4 text-neutral-400" />
            <div>
              <p className="text-sm text-neutral-600">Created</p>
              <p className="font-medium">{new Date(product.createdAt).toLocaleDateString()}</p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <Calendar className="h-4 w-4 text-neutral-400" />
            <div>
              <p className="text-sm text-neutral-600">Last Updated</p>
              <p className="font-medium">{new Date(product.updatedAt).toLocaleDateString()}</p>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

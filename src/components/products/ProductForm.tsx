'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Textarea } from '@/components/ui/Textarea';
import { Select, SelectOption } from '@/components/ui/Select';
import { Label } from '@/components/ui/Label';
import { ImageUpload } from '@/components/ui/ImageUpload';
import { ProductFormData } from '@/types/product';
import { Category } from '@/types/category';
import { generateSlug } from '@/lib/utils';

interface ProductFormProps {
  initialData?: Partial<ProductFormData>;
  categories: Category[];
  onSubmit: (data: ProductFormData) => void;
  onCancel: () => void;
  isLoading?: boolean;
  mode?: 'create' | 'edit';
}

const statusOptions: SelectOption[] = [
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
  { value: 'draft', label: 'Draft' },
];

export const ProductForm: React.FC<ProductFormProps> = ({
  initialData,
  categories,
  onSubmit,
  onCancel,
  isLoading = false,
  mode = 'create',
}) => {
  const [formData, setFormData] = useState<ProductFormData>({
    name: initialData?.name || '',
    description: initialData?.description || '',
    categoryId: initialData?.categoryId || 0,
    price: initialData?.price || 0,
    costPrice: initialData?.costPrice || 0,
    stock: initialData?.stock || 0,
    minStock: initialData?.minStock || 10,
    maxStock: initialData?.maxStock || 1000,
    sku: initialData?.sku || '',
    barcode: initialData?.barcode || '',
    weight: initialData?.weight || 0,
    dimensions: initialData?.dimensions || {
      length: 0,
      width: 0,
      height: 0,
    },
    status: initialData?.status || 'active',
    featured: initialData?.featured || false,
    warranty: initialData?.warranty || '',
    brand: initialData?.brand || '',
    model: initialData?.model || '',
    images: initialData?.images || [],
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSkuManuallyEdited, setIsSkuManuallyEdited] = useState(false);

  // Auto-generate SKU from name if not manually edited
  useEffect(() => {
    if (!isSkuManuallyEdited && formData.name) {
      const generatedSku = generateSlug(formData.name).toUpperCase().replace(/-/g, '-');
      setFormData(prev => ({
        ...prev,
        sku: generatedSku,
      }));
    }
  }, [formData.name, isSkuManuallyEdited]);

  const categoryOptions: SelectOption[] = categories.map(category => ({
    value: category.id.toString(),
    label: category.name,
  }));

  const handleInputChange = (field: keyof ProductFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  const handleSkuChange = (value: string) => {
    setIsSkuManuallyEdited(true);
    handleInputChange('sku', value);
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Product name is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (!formData.categoryId || formData.categoryId === 0) {
      newErrors.categoryId = 'Category is required';
    }

    if (formData.price <= 0) {
      newErrors.price = 'Price must be greater than 0';
    }

    if (formData.stock < 0) {
      newErrors.stock = 'Stock cannot be negative';
    }

    if (!formData.sku.trim()) {
      newErrors.sku = 'SKU is required';
    }

    if (formData.images.length === 0) {
      newErrors.images = 'At least one product image is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Basic Information */}
        <div className="md:col-span-2">
          <h3 className="text-lg font-medium text-neutral-900 mb-4">Basic Information</h3>
        </div>

        <div className="md:col-span-2">
          <Input
            label="Product Name"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder="Enter product name"
            error={errors.name}
            required
            fullWidth
          />
        </div>

        <div className="md:col-span-2">
          <Textarea
            label="Description"
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="Enter product description"
            error={errors.description}
            rows={4}
            required
            fullWidth
          />
        </div>

        <div>
          <Select
            label="Category"
            options={categoryOptions}
            value={formData.categoryId.toString()}
            onChange={(value) => handleInputChange('categoryId', parseInt(value))}
            placeholder="Select a category"
            error={errors.categoryId}
            required
            fullWidth
          />
        </div>

        <div>
          <Select
            label="Status"
            options={statusOptions}
            value={formData.status}
            onChange={(value) => handleInputChange('status', value)}
            fullWidth
          />
        </div>

        {/* Pricing */}
        <div className="md:col-span-2">
          <h3 className="text-lg font-medium text-neutral-900 mb-4 mt-6">Pricing</h3>
        </div>

        <div>
          <Input
            label="Selling Price"
            type="number"
            step="0.01"
            min="0"
            value={formData.price}
            onChange={(e) => handleInputChange('price', parseFloat(e.target.value) || 0)}
            placeholder="0.00"
            error={errors.price}
            required
            fullWidth
          />
        </div>

        <div>
          <Input
            label="Cost Price"
            type="number"
            step="0.01"
            min="0"
            value={formData.costPrice}
            onChange={(e) => handleInputChange('costPrice', parseFloat(e.target.value) || 0)}
            placeholder="0.00"
            fullWidth
          />
        </div>

        {/* Inventory */}
        <div className="md:col-span-2">
          <h3 className="text-lg font-medium text-neutral-900 mb-4 mt-6">Inventory</h3>
        </div>

        <div>
          <Input
            label="Current Stock"
            type="number"
            min="0"
            value={formData.stock}
            onChange={(e) => handleInputChange('stock', parseInt(e.target.value) || 0)}
            placeholder="0"
            error={errors.stock}
            required
            fullWidth
          />
        </div>

        <div>
          <Input
            label="Minimum Stock"
            type="number"
            min="0"
            value={formData.minStock}
            onChange={(e) => handleInputChange('minStock', parseInt(e.target.value) || 0)}
            placeholder="10"
            fullWidth
          />
        </div>

        <div>
          <Input
            label="SKU"
            value={formData.sku}
            onChange={(e) => handleSkuChange(e.target.value)}
            placeholder="PRODUCT-SKU"
            error={errors.sku}
            helperText="Stock Keeping Unit - will be auto-generated if left empty"
            required
            fullWidth
          />
        </div>

        <div>
          <Input
            label="Barcode"
            value={formData.barcode}
            onChange={(e) => handleInputChange('barcode', e.target.value)}
            placeholder="Enter barcode"
            fullWidth
          />
        </div>

        {/* Product Details */}
        <div className="md:col-span-2">
          <h3 className="text-lg font-medium text-neutral-900 mb-4 mt-6">Product Details</h3>
        </div>

        <div>
          <Input
            label="Brand"
            value={formData.brand}
            onChange={(e) => handleInputChange('brand', e.target.value)}
            placeholder="Enter brand name"
            fullWidth
          />
        </div>

        <div>
          <Input
            label="Model"
            value={formData.model}
            onChange={(e) => handleInputChange('model', e.target.value)}
            placeholder="Enter model number"
            fullWidth
          />
        </div>

        <div>
          <Input
            label="Weight (kg)"
            type="number"
            step="0.01"
            min="0"
            value={formData.weight}
            onChange={(e) => handleInputChange('weight', parseFloat(e.target.value) || 0)}
            placeholder="0.00"
            fullWidth
          />
        </div>

        <div>
          <Input
            label="Warranty"
            value={formData.warranty}
            onChange={(e) => handleInputChange('warranty', e.target.value)}
            placeholder="e.g., 2 years"
            fullWidth
          />
        </div>

        {/* Product Images */}
        <div className="md:col-span-2">
          <h3 className="text-lg font-medium text-neutral-900 mb-4 mt-6">Product Images</h3>
        </div>

        <div className="md:col-span-2">
          <ImageUpload
            label="Product Images"
            value={formData.images}
            onChange={(images) => handleInputChange('images', images)}
            maxImages={5}
            helperText="Upload up to 5 images. The first image will be used as the primary image."
            required
          />
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-4 pt-6 border-t border-neutral-200">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          loading={isLoading}
          disabled={isLoading}
        >
          {mode === 'create' ? 'Create Product' : 'Update Product'}
        </Button>
      </div>
    </form>
  );
};

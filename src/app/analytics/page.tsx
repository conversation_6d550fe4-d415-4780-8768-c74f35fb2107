import { AdminLayout } from '@/components/layout/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  Calendar,
  BarChart3,
  PieChart,
  Download
} from 'lucide-react';
import { formatCurrency, formatPercent } from '@/lib/utils';

// Mock analytics data
const analyticsData = {
  revenue: {
    current: 125000,
    previous: 98000,
    growth: 27.6,
  },
  orders: {
    current: 342,
    previous: 289,
    growth: 18.3,
  },
  customers: {
    current: 1247,
    previous: 1156,
    growth: 7.9,
  },
  products: {
    current: 89,
    previous: 85,
    growth: 4.7,
  },
};

const topProducts = [
  { name: 'Solar Panel 300W', sales: 45, revenue: 13455 },
  { name: 'Lithium Battery 100Ah', sales: 28, revenue: 25172 },
  { name: 'MPPT Controller 40A', sales: 35, revenue: 5599 },
  { name: 'Pure Sine Wave Inverter', sales: 18, revenue: 8099 },
];

const salesByCategory = [
  { category: 'Solar Panels', percentage: 42, amount: 52500 },
  { category: 'Batteries', percentage: 35, amount: 43750 },
  { category: 'Inverters', percentage: 15, amount: 18750 },
  { category: 'Controllers', percentage: 8, amount: 10000 },
];

const recentActivity = [
  { type: 'order', description: 'New order #ORD-1234 placed', time: '2 minutes ago' },
  { type: 'customer', description: 'New customer registration', time: '15 minutes ago' },
  { type: 'product', description: 'Low stock alert: Solar Panel 300W', time: '1 hour ago' },
  { type: 'order', description: 'Order #ORD-1230 shipped', time: '2 hours ago' },
];

export default function AnalyticsPage() {
  return (
    <AdminLayout
      title="Analytics"
      subtitle="Business insights and performance metrics"
    >
      <div className="space-y-8">
        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-3">
                    <div className="h-10 w-10 bg-success-100 rounded-lg flex items-center justify-center shadow-sm">
                      <DollarSign className="h-5 w-5 text-success-600" />
                    </div>
                    <p className="text-sm font-medium text-neutral-600 truncate">
                      Revenue
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-2xl font-bold text-neutral-900 leading-none">
                      {formatCurrency(analyticsData.revenue.current)}
                    </p>
                    <div className="flex items-center space-x-1">
                      <TrendingUp className="h-3 w-3 text-success-600" />
                      <p className="text-xs text-success-600 font-medium">
                        +{analyticsData.revenue.growth}%
                      </p>
                      <p className="text-xs text-neutral-500">
                        vs last month
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-3">
                    <div className="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center shadow-sm">
                      <ShoppingCart className="h-5 w-5 text-blue-600" />
                    </div>
                    <p className="text-sm font-medium text-neutral-600 truncate">
                      Orders
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-2xl font-bold text-neutral-900 leading-none">
                      {analyticsData.orders.current}
                    </p>
                    <div className="flex items-center space-x-1">
                      <TrendingUp className="h-3 w-3 text-success-600" />
                      <p className="text-xs text-success-600 font-medium">
                        +{analyticsData.orders.growth}%
                      </p>
                      <p className="text-xs text-neutral-500">
                        vs last month
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-3">
                    <div className="h-10 w-10 bg-purple-100 rounded-lg flex items-center justify-center shadow-sm">
                      <Users className="h-5 w-5 text-purple-600" />
                    </div>
                    <p className="text-sm font-medium text-neutral-600 truncate">
                      Customers
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-2xl font-bold text-neutral-900 leading-none">
                      {analyticsData.customers.current}
                    </p>
                    <div className="flex items-center space-x-1">
                      <TrendingUp className="h-3 w-3 text-success-600" />
                      <p className="text-xs text-success-600 font-medium">
                        +{analyticsData.customers.growth}%
                      </p>
                      <p className="text-xs text-neutral-500">
                        vs last month
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-3">
                    <div className="h-10 w-10 bg-orange-100 rounded-lg flex items-center justify-center shadow-sm">
                      <Package className="h-5 w-5 text-orange-600" />
                    </div>
                    <p className="text-sm font-medium text-neutral-600 truncate">
                      Products
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-2xl font-bold text-neutral-900 leading-none">
                      {analyticsData.products.current}
                    </p>
                    <div className="flex items-center space-x-1">
                      <TrendingUp className="h-3 w-3 text-success-600" />
                      <p className="text-xs text-success-600 font-medium">
                        +{analyticsData.products.growth}%
                      </p>
                      <p className="text-xs text-neutral-500">
                        vs last month
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Top Products */}
          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center text-lg">
                  <div className="h-8 w-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                    <BarChart3 className="h-4 w-4 text-blue-600" />
                  </div>
                  Top Products
                </CardTitle>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-4">
                {topProducts.map((product, index) => (
                  <div key={product.name} className={`flex items-center justify-between py-3 ${index !== topProducts.length - 1 ? 'border-b border-neutral-100' : ''}`}>
                    <div className="flex items-center space-x-3 flex-1 min-w-0">
                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary-100 text-primary-600 text-sm font-medium flex-shrink-0">
                        {index + 1}
                      </div>
                      <div className="min-w-0 flex-1">
                        <p className="font-medium text-neutral-900 truncate">{product.name}</p>
                        <p className="text-sm text-neutral-500">{product.sales} units sold</p>
                      </div>
                    </div>
                    <div className="text-right ml-4">
                      <p className="font-semibold text-neutral-900">
                        {formatCurrency(product.revenue)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Sales by Category */}
          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center text-lg">
                  <div className="h-8 w-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                    <PieChart className="h-4 w-4 text-green-600" />
                  </div>
                  Sales by Category
                </CardTitle>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-6">
                {salesByCategory.map((category, index) => (
                  <div key={category.category} className={`space-y-3 ${index !== salesByCategory.length - 1 ? 'pb-6 border-b border-neutral-100' : ''}`}>
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-neutral-900">{category.category}</span>
                      <span className="text-sm font-medium text-neutral-600">{category.percentage}%</span>
                    </div>
                    <div className="w-full bg-neutral-200 rounded-full h-3">
                      <div
                        className="bg-primary-600 h-3 rounded-full transition-all duration-300"
                        style={{ width: `${category.percentage}%` }}
                      ></div>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-neutral-500">Revenue</span>
                      <span className="font-medium text-neutral-900">{formatCurrency(category.amount)}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-lg">
              <div className="h-8 w-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                <Calendar className="h-4 w-4 text-purple-600" />
              </div>
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-4">
              {recentActivity.map((activity, index) => (
                <div key={index} className={`flex items-start space-x-4 py-3 ${index !== recentActivity.length - 1 ? 'border-b border-neutral-100' : ''}`}>
                  <div className={`h-3 w-3 rounded-full mt-1 flex-shrink-0 ${
                    activity.type === 'order' ? 'bg-blue-500' :
                    activity.type === 'customer' ? 'bg-green-500' :
                    'bg-orange-500'
                  }`}></div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-neutral-900 mb-1">{activity.description}</p>
                    <p className="text-xs text-neutral-500">{activity.time}</p>
                  </div>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                    activity.type === 'order' ? 'bg-blue-100 text-blue-700' :
                    activity.type === 'customer' ? 'bg-green-100 text-green-700' :
                    'bg-orange-100 text-orange-700'
                  }`}>
                    {activity.type}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}

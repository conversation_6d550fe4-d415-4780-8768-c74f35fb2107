import { AdminLayout } from '@/components/layout/AdminLayout';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  Cpu,
  Zap,
  Settings
} from 'lucide-react';
import { formatCurrency, getStockStatus } from '@/lib/utils';

const controllers = [
  {
    id: 1,
    name: 'MPPT Solar Charge Controller 40A',
    type: 'MPPT',
    amperage: 40,
    voltage: '12V/24V',
    price: 159.99,
    stock: 23,
    minStock: 5,
    efficiency: 98.5,
    warranty: '5 years',
    status: 'active',
    sku: 'CTRL-MPPT-40A',
  },
  {
    id: 2,
    name: 'PWM Solar Charge Controller 30A',
    type: 'PWM',
    amperage: 30,
    voltage: '12V/24V',
    price: 89.99,
    stock: 15,
    minStock: 8,
    efficiency: 85.0,
    warranty: '3 years',
    status: 'active',
    sku: 'CTRL-PWM-30A',
  },
  {
    id: 3,
    name: 'Smart MPPT Controller 60A with Bluetooth',
    type: 'MPPT',
    amperage: 60,
    voltage: '12V/24V/48V',
    price: 299.99,
    stock: 8,
    minStock: 10,
    efficiency: 99.2,
    warranty: '7 years',
    status: 'active',
    sku: 'CTRL-SMART-60A',
  },
];

export default function ControllersPage() {
  return (
    <AdminLayout
      title="Controllers"
      subtitle="Charge controllers and system management"
    >
      <div className="space-y-8">
        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex flex-col sm:flex-row gap-4 flex-1">
            <div className="relative flex-1 max-w-md">
              <Input
                type="search"
                placeholder="Search controllers..."
                leftIcon={<Search className="h-4 w-4" />}
                className="pl-10"
              />
            </div>
            <Button variant="outline" className="whitespace-nowrap">
              <Filter className="h-4 w-4 mr-2" />
              Filter by Type
            </Button>
          </div>
          <Button variant="default" className="whitespace-nowrap">
            <Plus className="h-4 w-4 mr-2" />
            Add Controller
          </Button>
        </div>

        {/* Controllers Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {controllers.map((controller) => {
            const stockStatus = getStockStatus(controller.stock, controller.minStock);

            return (
              <Card key={controller.id} className="group hover:shadow-strong transition-all duration-200">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {/* Controller Image */}
                    <div className="aspect-video bg-gradient-to-br from-purple-50 to-blue-50 rounded-lg flex items-center justify-center border-2 border-dashed border-neutral-200">
                      <Cpu className="h-16 w-16 text-purple-600" />
                    </div>

                    {/* Controller Info */}
                    <div className="space-y-3">
                      <div className="flex items-start justify-between">
                        <h3 className="font-semibold text-neutral-900 line-clamp-2">
                          {controller.name}
                        </h3>
                        <Badge variant="success" size="sm">
                          {controller.status}
                        </Badge>
                      </div>

                      <div className="grid grid-cols-2 gap-3 text-sm">
                        <div className="flex items-center gap-2">
                          <Zap className="h-4 w-4 text-blue-600" />
                          <span className="font-medium">{controller.amperage}A</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Settings className="h-4 w-4 text-green-600" />
                          <span className="font-medium">{controller.efficiency}%</span>
                        </div>
                      </div>

                      <div className="space-y-2 text-sm text-neutral-600">
                        <p><span className="font-medium">Type:</span> {controller.type}</p>
                        <p><span className="font-medium">Voltage:</span> {controller.voltage}</p>
                        <p><span className="font-medium">Warranty:</span> {controller.warranty}</p>
                        <p><span className="font-medium">SKU:</span> {controller.sku}</p>
                      </div>

                      <div className="flex items-center justify-between pt-2 border-t border-neutral-200">
                        <span className="text-xl font-bold text-neutral-900">
                          {formatCurrency(controller.price)}
                        </span>
                        <Badge
                          variant={stockStatus.status === 'in-stock' ? 'success' : 'warning'}
                          size="sm"
                        >
                          {controller.stock} in stock
                        </Badge>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2 pt-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        <Eye className="h-3 w-3 mr-1" />
                        View
                      </Button>
                      <Button variant="outline" size="sm" className="flex-1">
                        <Edit className="h-3 w-3 mr-1" />
                        Edit
                      </Button>
                      <Button variant="outline" size="sm">
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Controller Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Total Controllers</p>
                  <p className="text-2xl font-bold text-neutral-900">{controllers.length}</p>
                </div>
                <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Cpu className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Avg Efficiency</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {(controllers.reduce((sum, ctrl) => sum + ctrl.efficiency, 0) / controllers.length).toFixed(1)}%
                  </p>
                </div>
                <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <Settings className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Total Value</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {formatCurrency(controllers.reduce((sum, ctrl) => sum + (ctrl.price * ctrl.stock), 0))}
                  </p>
                </div>
                <div className="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center">
                  <Cpu className="h-6 w-6 text-primary-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
  );
}

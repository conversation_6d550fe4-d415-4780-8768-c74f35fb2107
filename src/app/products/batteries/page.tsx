import { AdminLayout } from '@/components/layout/AdminLayout';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import { 
  Plus, 
  Search, 
  Filter,
  Edit,
  Trash2,
  Eye,
  Battery,
  Zap,

} from 'lucide-react';
import { formatCurrency, getStockStatus } from '@/lib/utils';

const batteries = [
  {
    id: 1,
    name: 'LiFePO4 Battery 100Ah 12V',
    capacity: 100,
    voltage: 12,
    type: 'LiFePO4',
    price: 899.99,
    stock: 8,
    minStock: 10,
    cycles: 6000,
    warranty: '10 years',
    status: 'active',
    sku: 'BAT-LIFEPO4-100AH',
  },
  {
    id: 2,
    name: 'AGM Deep Cycle Battery 200Ah 12V',
    capacity: 200,
    voltage: 12,
    type: 'AGM',
    price: 449.99,
    stock: 15,
    minStock: 8,
    cycles: 1200,
    warranty: '5 years',
    status: 'active',
    sku: 'BAT-AGM-200AH',
  },
];

export default function BatteriesPage() {
  return (
    <AdminLayout 
      title="Batteries" 
      subtitle="Energy storage solutions for solar systems"
    >
      <div className="space-y-8">
        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex flex-col sm:flex-row gap-4 flex-1">
            <div className="relative flex-1 max-w-md">
              <Input
                type="search"
                placeholder="Search batteries..."
                leftIcon={<Search className="h-4 w-4" />}
                className="pl-10"
              />
            </div>
            <Button variant="outline" className="whitespace-nowrap">
              <Filter className="h-4 w-4 mr-2" />
              Filter by Type
            </Button>
          </div>
          <Button variant="default" className="whitespace-nowrap">
            <Plus className="h-4 w-4 mr-2" />
            Add Battery
          </Button>
        </div>

        {/* Batteries Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {batteries.map((battery) => {
            const stockStatus = getStockStatus(battery.stock, battery.minStock);
            
            return (
              <Card key={battery.id} className="group hover:shadow-strong transition-all duration-200">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {/* Battery Image */}
                    <div className="aspect-video bg-gradient-to-br from-green-50 to-blue-50 rounded-lg flex items-center justify-center border-2 border-dashed border-neutral-200">
                      <Battery className="h-16 w-16 text-green-600" />
                    </div>
                    
                    {/* Battery Info */}
                    <div className="space-y-3">
                      <div className="flex items-start justify-between">
                        <h3 className="font-semibold text-neutral-900 line-clamp-2">
                          {battery.name}
                        </h3>
                        <Badge variant="success" size="sm">
                          {battery.status}
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-3 text-sm">
                        <div className="flex items-center gap-2">
                          <Battery className="h-4 w-4 text-green-600" />
                          <span className="font-medium">{battery.capacity}Ah</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Zap className="h-4 w-4 text-blue-600" />
                          <span className="font-medium">{battery.voltage}V</span>
                        </div>
                      </div>
                      
                      <div className="space-y-2 text-sm text-neutral-600">
                        <p><span className="font-medium">Type:</span> {battery.type}</p>
                        <p><span className="font-medium">Cycles:</span> {battery.cycles.toLocaleString()}</p>
                        <p><span className="font-medium">Warranty:</span> {battery.warranty}</p>
                        <p><span className="font-medium">SKU:</span> {battery.sku}</p>
                      </div>
                      
                      <div className="flex items-center justify-between pt-2 border-t border-neutral-200">
                        <span className="text-xl font-bold text-neutral-900">
                          {formatCurrency(battery.price)}
                        </span>
                        <Badge 
                          variant={stockStatus.status === 'in-stock' ? 'success' : 'warning'}
                          size="sm"
                        >
                          {battery.stock} in stock
                        </Badge>
                      </div>
                    </div>
                    
                    {/* Actions */}
                    <div className="flex gap-2 pt-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        <Eye className="h-3 w-3 mr-1" />
                        View
                      </Button>
                      <Button variant="outline" size="sm" className="flex-1">
                        <Edit className="h-3 w-3 mr-1" />
                        Edit
                      </Button>
                      <Button variant="outline" size="sm">
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </AdminLayout>
  );
}

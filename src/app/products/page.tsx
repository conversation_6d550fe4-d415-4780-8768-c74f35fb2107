'use client';

import React, { useState, useMemo, useEffect } from 'react';
import { AdminLayout } from '@/components/layout/AdminLayout';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import { Label } from '@/components/ui/Label';
import { Modal } from '@/components/ui/Modal';
import { ConfirmationModal } from '@/components/ui/ConfirmationModal';
import { useToast } from '@/components/ui/Toast';
import { Pagination } from '@/components/ui/Table';
import { ProductForm } from '@/components/products/ProductForm';
import { ProductDetailModal } from '@/components/products/ProductDetailModal';
import { Product, ProductFormData } from '@/types/product';
import { Category } from '@/types/category';
import { formatCurrency, getStockStatus } from '@/lib/utils';
import { productService } from '@/services/productService';
import { categoryService } from '@/services/categoryService';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Package,
  TrendingUp,
  AlertTriangle,
  Eye,
  Filter,
  Download,
  X
} from 'lucide-react';


export default function ProductsPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [productToDelete, setProductToDelete] = useState<Product | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [viewingProduct, setViewingProduct] = useState<Product | null>(null);

  // Filter states
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filters, setFilters] = useState({
    category: '',
    status: '',
    priceRange: { min: '', max: '' },
    stockLevel: ''
  });

  const { addToast } = useToast();

  // Load products and categories from Firebase with real-time updates
  useEffect(() => {
    setIsInitialLoading(true);

    // Set up real-time listener for categories
    const unsubscribeCategories = categoryService.onCategoriesChange((fetchedCategories) => {
      console.log('Categories updated in products page:', fetchedCategories);
      setCategories(fetchedCategories);
    });

    // Load products initially and set up manual refresh (since products don't have real-time listeners yet)
    const loadProducts = async () => {
      try {
        const fetchedProducts = await productService.getAllProducts();
        console.log('Products loaded:', fetchedProducts);
        setProducts(fetchedProducts);
      } catch (error) {
        console.error('Error loading products:', error);
        addToast({
          type: 'error',
          title: 'Error',
          message: 'Failed to load products. Please refresh the page.',
        });
      } finally {
        setIsInitialLoading(false);
      }
    };

    loadProducts();

    // Cleanup function
    return () => {
      unsubscribeCategories();
    };
  }, []); // Remove addToast dependency to prevent infinite loop

  // Filter and paginate products
  const filteredProducts = useMemo(() => {
    return products.filter(product => {
      // Search term filter
      const matchesSearch = !searchTerm || (
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.category.toLowerCase().includes(searchTerm.toLowerCase())
      );

      // Category filter
      const matchesCategory = !filters.category || product.categoryId === filters.category;

      // Status filter
      const matchesStatus = !filters.status || product.status === filters.status;

      // Price range filter
      const matchesPriceRange = (!filters.priceRange.min || product.price >= parseFloat(filters.priceRange.min)) &&
                               (!filters.priceRange.max || product.price <= parseFloat(filters.priceRange.max));

      // Stock level filter
      let matchesStockLevel = true;
      if (filters.stockLevel === 'low') {
        matchesStockLevel = product.stock <= product.minStock;
      } else if (filters.stockLevel === 'out') {
        matchesStockLevel = product.stock === 0;
      } else if (filters.stockLevel === 'in-stock') {
        matchesStockLevel = product.stock > product.minStock;
      }

      return matchesSearch && matchesCategory && matchesStatus && matchesPriceRange && matchesStockLevel;
    });
  }, [products, searchTerm, filters]);

  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedProducts = filteredProducts.slice(startIndex, startIndex + itemsPerPage);

  // Reset to first page when search or filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, filters]);

  // Filter handlers
  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const clearFilters = () => {
    setFilters({
      category: '',
      status: '',
      priceRange: { min: '', max: '' },
      stockLevel: ''
    });
  };

  const hasActiveFilters = filters.category || filters.status || filters.priceRange.min || filters.priceRange.max || filters.stockLevel;

  // Export functionality
  const exportToCSV = () => {
    const headers = [
      'Name', 'SKU', 'Category', 'Price', 'Cost Price', 'Stock', 'Min Stock', 'Status', 'Weight', 'Dimensions', 'Created At'
    ];

    const csvData = filteredProducts.map(product => [
      product.name,
      product.sku,
      product.category,
      product.price,
      product.costPrice || 0,
      product.stock,
      product.minStock,
      product.status,
      product.weight || 0,
      `${product.dimensions?.length || 0}x${product.dimensions?.width || 0}x${product.dimensions?.height || 0}`,
      new Date(product.createdAt).toLocaleDateString()
    ]);

    const csvContent = [headers, ...csvData]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `products-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleCreateProduct = async (data: ProductFormData) => {
    setIsLoading(true);
    try {
      const category = categories.find(c => c.id === data.categoryId);
      const productData = {
        ...data,
        category: category?.name || '',
        // Use the actual uploaded images from the form
        images: data.images.length > 0 ? data.images : ['/api/placeholder/400/400'],
      };

      await productService.createProduct(productData);

      // Reload products to get the updated list
      const updatedProducts = await productService.getAllProducts();
      setProducts(updatedProducts);

      setIsFormModalOpen(false);

      addToast({
        type: 'success',
        title: 'Product Created',
        message: `${data.name} has been created successfully.`,
      });
    } catch (error) {
      console.error('Error creating product:', error);
      addToast({
        type: 'error',
        title: 'Error',
        message: 'Failed to create product. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateProduct = async (data: ProductFormData) => {
    if (!editingProduct) return;

    setIsLoading(true);
    try {
      const category = categories.find(c => c.id === data.categoryId);
      const updateData = {
        ...data,
        category: category?.name || editingProduct.category,
      };

      await productService.updateProduct(editingProduct.id, updateData);

      // Reload products to get the updated list
      const updatedProducts = await productService.getAllProducts();
      setProducts(updatedProducts);

      setIsFormModalOpen(false);
      setEditingProduct(null);

      addToast({
        type: 'success',
        title: 'Product Updated',
        message: `${data.name} has been updated successfully.`,
      });
    } catch (error) {
      console.error('Error updating product:', error);
      addToast({
        type: 'error',
        title: 'Error',
        message: 'Failed to update product. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteProduct = async () => {
    if (!productToDelete) return;

    setIsDeleting(true);
    try {
      await productService.deleteProduct(productToDelete.id);

      // Reload products to get the updated list
      const updatedProducts = await productService.getAllProducts();
      setProducts(updatedProducts);

      setIsDeleteModalOpen(false);
      setProductToDelete(null);

      addToast({
        type: 'success',
        title: 'Product Deleted',
        message: `${productToDelete.name} has been deleted successfully.`,
      });
    } catch (error) {
      console.error('Error deleting product:', error);
      addToast({
        type: 'error',
        title: 'Error',
        message: 'Failed to delete product. Please try again.',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const openCreateModal = () => {
    setEditingProduct(null);
    setIsFormModalOpen(true);
  };

  const openEditModal = (product: Product) => {
    setEditingProduct(product);
    setIsFormModalOpen(true);
  };

  const closeModal = () => {
    setIsFormModalOpen(false);
    setEditingProduct(null);
  };

  const openDeleteModal = (product: Product) => {
    setProductToDelete(product);
    setIsDeleteModalOpen(true);
  };

  const closeDeleteModal = () => {
    setIsDeleteModalOpen(false);
    setProductToDelete(null);
  };

  const openViewModal = (product: Product) => {
    setViewingProduct(product);
    setIsViewModalOpen(true);
  };

  const closeViewModal = () => {
    setIsViewModalOpen(false);
    setViewingProduct(null);
  };

  // Show loading state while fetching data
  if (isInitialLoading) {
    return (
      <AdminLayout
        title="Products"
        subtitle="Manage your solar equipment inventory"
      >
        <div className="space-y-8">
          {/* Loading Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {Array.from({ length: 4 }).map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="animate-pulse">
                    <div className="flex items-center justify-between">
                      <div className="space-y-2">
                        <div className="h-4 bg-neutral-200 rounded w-24"></div>
                        <div className="h-8 bg-neutral-200 rounded w-16"></div>
                      </div>
                      <div className="h-12 w-12 bg-neutral-200 rounded-lg"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Loading Table */}
          <Card>
            <CardContent className="p-6">
              <div className="animate-pulse space-y-4">
                <div className="flex justify-between items-center">
                  <div className="h-10 bg-neutral-200 rounded w-64"></div>
                  <div className="h-10 bg-neutral-200 rounded w-32"></div>
                </div>
                <div className="space-y-3">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="h-16 bg-neutral-200 rounded"></div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title="Products"
      subtitle="Manage your solar equipment inventory"
    >
      <div className="space-y-8">
        {/* Product Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Total Products</p>
                  <p className="text-2xl font-bold text-neutral-900">{products.length}</p>
                </div>
                <div className="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center">
                  <Package className="h-6 w-6 text-primary-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Total Value</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {formatCurrency(products.reduce((sum, p) => sum + (p.price * p.stock), 0))}
                   
                  </p>
                </div>
                <div className="h-12 w-12 bg-success-100 rounded-lg flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-success-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Low Stock</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {products.filter(p => p.stock <= p.minStock).length}
                  </p>
                </div>
                <div className="h-12 w-12 bg-warning-100 rounded-lg flex items-center justify-center">
                  <AlertTriangle className="h-6 w-6 text-warning-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Out of Stock</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {products.filter(p => p.stock === 0).length}
                  </p>
                </div>
                <div className="h-12 w-12 bg-error-100 rounded-lg flex items-center justify-center">
                  <AlertTriangle className="h-6 w-6 text-error-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="relative flex-1 max-w-md">
            <Input
              type="search"
              placeholder="Search products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              leftIcon={<Search className="h-4 w-4" />}
              className="pl-10"
            />
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={() => setIsFilterOpen(!isFilterOpen)}
              className={`whitespace-nowrap ${hasActiveFilters ? 'bg-primary-50 border-primary-200' : ''}`}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filter
              {hasActiveFilters && (
                <span className="ml-2 bg-primary-500 text-white text-xs rounded-full px-2 py-0.5">
                  {Object.values(filters).filter(v => v && (typeof v === 'string' ? v : v.min || v.max)).length}
                </span>
              )}
            </Button>
            <Button variant="outline" onClick={exportToCSV} className="whitespace-nowrap">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="default" className="whitespace-nowrap" onClick={openCreateModal}>
              <Plus className="h-4 w-4 mr-2" />
              Add Product
            </Button>
          </div>
        </div>

        {/* Filter Panel */}
        {isFilterOpen && (
          <Card className="animate-in slide-in-from-top-2 duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-neutral-900">Filters</h3>
                <div className="flex items-center gap-2">
                  {hasActiveFilters && (
                    <Button variant="ghost" size="sm" onClick={clearFilters}>
                      Clear All
                    </Button>
                  )}
                  <Button variant="ghost" size="sm" onClick={() => setIsFilterOpen(false)}>
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Category Filter */}
                <div>
                  <Label htmlFor="category-filter">Category</Label>
                  <select
                    id="category-filter"
                    value={filters.category}
                    onChange={(e) => handleFilterChange('category', e.target.value)}
                    className="mt-1 block w-full rounded-md border border-neutral-300 px-3 py-2 text-sm focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500"
                  >
                    <option value="">All Categories</option>
                    {categories.map(category => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Status Filter */}
                <div>
                  <Label htmlFor="status-filter">Status</Label>
                  <select
                    id="status-filter"
                    value={filters.status}
                    onChange={(e) => handleFilterChange('status', e.target.value)}
                    className="mt-1 block w-full rounded-md border border-neutral-300 px-3 py-2 text-sm focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500"
                  >
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="draft">Draft</option>
                  </select>
                </div>

                {/* Stock Level Filter */}
                <div>
                  <Label htmlFor="stock-filter">Stock Level</Label>
                  <select
                    id="stock-filter"
                    value={filters.stockLevel}
                    onChange={(e) => handleFilterChange('stockLevel', e.target.value)}
                    className="mt-1 block w-full rounded-md border border-neutral-300 px-3 py-2 text-sm focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500"
                  >
                    <option value="">All Stock Levels</option>
                    <option value="in-stock">In Stock</option>
                    <option value="low">Low Stock</option>
                    <option value="out">Out of Stock</option>
                  </select>
                </div>

                {/* Price Range Filter */}
                <div>
                  <Label>Price Range</Label>
                  <div className="mt-1 flex gap-2">
                    <Input
                      type="number"
                      placeholder="Min"
                      value={filters.priceRange.min}
                      onChange={(e) => handleFilterChange('priceRange', { ...filters.priceRange, min: e.target.value })}
                      className="text-sm"
                    />
                    <Input
                      type="number"
                      placeholder="Max"
                      value={filters.priceRange.max}
                      onChange={(e) => handleFilterChange('priceRange', { ...filters.priceRange, max: e.target.value })}
                      className="text-sm"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {paginatedProducts.map((product) => {
            const stockStatus = getStockStatus(product.stock, product.minStock);

            return (
              <Card key={product.id} className="group hover:shadow-strong hover:scale-105 transition-all duration-200 cursor-pointer">
                <CardContent className="p-0">
                  {/* Product Image */}
                  <div className="aspect-square bg-gradient-to-br from-neutral-50 to-neutral-100 rounded-t-lg overflow-hidden">
                    {product.images && product.images.length > 0 ? (
                      <img
                        src={product.images[0]}
                        alt={product.name}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Package className="h-16 w-16 text-neutral-400" />
                      </div>
                    )}
                  </div>

                  {/* Product Info */}
                  <div className="p-4 space-y-3">
                    <div className="space-y-1">
                      <h3 className="font-semibold text-neutral-900 text-lg line-clamp-2">
                        {product.name}
                      </h3>
                      <p className="text-sm text-neutral-500">
                        {product.category} • {product.sku}
                      </p>
                      {product.brand && product.model && (
                        <p className="text-xs text-neutral-400">
                          {product.brand} {product.model}
                        </p>
                      )}
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <p className="text-2xl font-bold text-neutral-900">
                          {formatCurrency(product.price)}
                        </p>
                        <div className="flex items-center gap-2">
                          <Badge
                            variant={stockStatus.variant}
                            size="sm"
                          >
                            {stockStatus.label}
                          </Badge>
                          <span className="text-sm text-neutral-500">
                            {product.stock} in stock
                          </span>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge
                          variant={product.status === 'active' ? 'default' : product.status === 'draft' ? 'secondary' : 'secondary'}
                          size="sm"
                        >
                          {product.status === 'active' ? 'Active' : product.status === 'draft' ? 'Draft' : 'Inactive'}
                        </Badge>
                        {product.featured && (
                          <div className="mt-1">
                            <Badge variant="warning" size="sm">
                              Featured
                            </Badge>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2 pt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-1 hover:scale-105 transition-transform duration-200"
                        onClick={() => openViewModal(product)}
                      >
                        <Eye className="h-3 w-3 mr-1" />
                        View
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-1 hover:scale-105 transition-transform duration-200"
                        onClick={() => openEditModal(product)}
                      >
                        <Edit className="h-3 w-3 mr-1" />
                        Edit
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="hover:scale-105 transition-transform duration-200"
                        onClick={() => openDeleteModal(product)}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Pagination */}
        {filteredProducts.length > 0 && (
          <Card className="hover:shadow-md transition-shadow duration-200">
            <CardContent className="p-6">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
                itemsPerPage={itemsPerPage}
                totalItems={filteredProducts.length}
                onItemsPerPageChange={setItemsPerPage}
              />
            </CardContent>
          </Card>
        )}

        {/* Empty State */}
        {filteredProducts.length === 0 && (
          <Card className="hover:shadow-md transition-shadow duration-200">
            <CardContent className="p-12">
              <div className="text-center">
                <Package className="h-16 w-16 text-neutral-400 mx-auto mb-4" />
                <h3 className="text-xl font-medium text-neutral-900 mb-2">
                  {searchTerm ? 'No products found' : 'No products yet'}
                </h3>
                <p className="text-neutral-600 mb-6">
                  {searchTerm
                    ? 'Try adjusting your search terms.'
                    : 'Get started by creating your first product.'
                  }
                </p>
                {!searchTerm && (
                  <Button onClick={openCreateModal}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Product
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        )}

      </div>

      {/* Product Form Modal */}
      <Modal
        isOpen={isFormModalOpen}
        onClose={closeModal}
        title={editingProduct ? 'Edit Product' : 'Create New Product'}
        description={editingProduct ? 'Update the product details below.' : 'Fill in the details to create a new product.'}
        size="xl"
      >
        <ProductForm
          initialData={editingProduct || undefined}
          categories={categories}
          onSubmit={editingProduct ? handleUpdateProduct : handleCreateProduct}
          onCancel={closeModal}
          isLoading={isLoading}
          mode={editingProduct ? 'edit' : 'create'}
        />
      </Modal>

      {/* Product Detail Modal */}
      <ProductDetailModal
        isOpen={isViewModalOpen}
        onClose={closeViewModal}
        product={viewingProduct}
      />

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={closeDeleteModal}
        onConfirm={handleDeleteProduct}
        title="Delete Product"
        message={`Are you sure you want to delete "${productToDelete?.name}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        variant="danger"
        isLoading={isDeleting}
      />
    </AdminLayout>
  );
}

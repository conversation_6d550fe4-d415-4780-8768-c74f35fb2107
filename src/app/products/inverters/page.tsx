import { AdminLayout } from '@/components/layout/AdminLayout';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  Zap,
  Power,
  Activity
} from 'lucide-react';
import { formatCurrency, getStockStatus } from '@/lib/utils';

const inverters = [
  {
    id: 1,
    name: 'Pure Sine Wave Inverter 2000W',
    type: 'Pure Sine Wave',
    wattage: 2000,
    inputVoltage: '12V DC',
    outputVoltage: '120V AC',
    price: 449.99,
    stock: 12,
    minStock: 8,
    efficiency: 95.5,
    warranty: '3 years',
    status: 'active',
    sku: 'INV-PSW-2000W',
  },
  {
    id: 2,
    name: 'Modified Sine Wave Inverter 1500W',
    type: 'Modified Sine Wave',
    wattage: 1500,
    inputVoltage: '12V DC',
    outputVoltage: '120V AC',
    price: 299.99,
    stock: 18,
    minStock: 10,
    efficiency: 90.0,
    warranty: '2 years',
    status: 'active',
    sku: 'INV-MSW-1500W',
  },
  {
    id: 3,
    name: 'Grid-Tie Inverter 5000W',
    type: 'Grid-Tie',
    wattage: 5000,
    inputVoltage: '48V DC',
    outputVoltage: '240V AC',
    price: 1299.99,
    stock: 6,
    minStock: 5,
    efficiency: 97.8,
    warranty: '10 years',
    status: 'active',
    sku: 'INV-GT-5000W',
  },
];

export default function InvertersPage() {
  return (
    <AdminLayout
      title="Inverters"
      subtitle="DC to AC power conversion equipment"
    >
      <div className="space-y-8">
        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex flex-col sm:flex-row gap-4 flex-1">
            <div className="relative flex-1 max-w-md">
              <Input
                type="search"
                placeholder="Search inverters..."
                leftIcon={<Search className="h-4 w-4" />}
                className="pl-10"
              />
            </div>
            <Button variant="outline" className="whitespace-nowrap">
              <Filter className="h-4 w-4 mr-2" />
              Filter by Type
            </Button>
          </div>
          <Button variant="default" className="whitespace-nowrap">
            <Plus className="h-4 w-4 mr-2" />
            Add Inverter
          </Button>
        </div>

        {/* Inverters Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {inverters.map((inverter) => {
            const stockStatus = getStockStatus(inverter.stock, inverter.minStock);

            return (
              <Card key={inverter.id} className="group hover:shadow-strong transition-all duration-200">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {/* Inverter Image */}
                    <div className="aspect-video bg-gradient-to-br from-orange-50 to-red-50 rounded-lg flex items-center justify-center border-2 border-dashed border-neutral-200">
                      <Power className="h-16 w-16 text-orange-600" />
                    </div>

                    {/* Inverter Info */}
                    <div className="space-y-3">
                      <div className="flex items-start justify-between">
                        <h3 className="font-semibold text-neutral-900 line-clamp-2">
                          {inverter.name}
                        </h3>
                        <Badge variant="success" size="sm">
                          {inverter.status}
                        </Badge>
                      </div>

                      <div className="grid grid-cols-2 gap-3 text-sm">
                        <div className="flex items-center gap-2">
                          <Zap className="h-4 w-4 text-orange-600" />
                          <span className="font-medium">{inverter.wattage}W</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Activity className="h-4 w-4 text-green-600" />
                          <span className="font-medium">{inverter.efficiency}%</span>
                        </div>
                      </div>

                      <div className="space-y-2 text-sm text-neutral-600">
                        <p><span className="font-medium">Type:</span> {inverter.type}</p>
                        <p><span className="font-medium">Input:</span> {inverter.inputVoltage}</p>
                        <p><span className="font-medium">Output:</span> {inverter.outputVoltage}</p>
                        <p><span className="font-medium">Warranty:</span> {inverter.warranty}</p>
                        <p><span className="font-medium">SKU:</span> {inverter.sku}</p>
                      </div>

                      <div className="flex items-center justify-between pt-2 border-t border-neutral-200">
                        <span className="text-xl font-bold text-neutral-900">
                          {formatCurrency(inverter.price)}
                        </span>
                        <Badge
                          variant={stockStatus.status === 'in-stock' ? 'success' : 'warning'}
                          size="sm"
                        >
                          {inverter.stock} in stock
                        </Badge>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2 pt-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        <Eye className="h-3 w-3 mr-1" />
                        View
                      </Button>
                      <Button variant="outline" size="sm" className="flex-1">
                        <Edit className="h-3 w-3 mr-1" />
                        Edit
                      </Button>
                      <Button variant="outline" size="sm">
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Inverter Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Total Inverters</p>
                  <p className="text-2xl font-bold text-neutral-900">{inverters.length}</p>
                </div>
                <div className="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <Power className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Total Wattage</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {inverters.reduce((sum, inv) => sum + (inv.wattage * inv.stock), 0).toLocaleString()}W
                  </p>
                </div>
                <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Zap className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Avg Efficiency</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {(inverters.reduce((sum, inv) => sum + inv.efficiency, 0) / inverters.length).toFixed(1)}%
                  </p>
                </div>
                <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <Activity className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
  );
}

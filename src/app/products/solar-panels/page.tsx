// import { AdminLayout } from '@/components/layout/AdminLayout';
// import { Card, CardContent } from '@/components/ui/Card';
// import { Button } from '@/components/ui/Button';
// import { Input } from '@/components/ui/Input';
// import { Badge } from '@/components/ui/Badge';
// import { 
//   Plus, 
//   Search, 
//   Filter,
//   Edit,
//   Trash2,
//   Eye,
//   Sun,
//   Zap,
//   Settings
// } from 'lucide-react';
// import { formatCurrency, getStockStatus } from '@/lib/utils';

// // Mock data for solar panels
// const solarPanels = [
//   {
//     id: 1,
//     name: 'Monocrystalline Solar Panel 300W',
//     wattage: 300,
//     efficiency: 20.5,
//     price: 299.99,
//     stock: 45,
//     minStock: 10,
//     voltage: 24,
//     dimensions: '1956x992x40mm',
//     warranty: '25 years',
//     status: 'active',
//     sku: 'SP-MONO-300W',
//   },
//   {
//     id: 2,
//     name: 'Polycrystalline Solar Panel 250W',
//     wattage: 250,
//     efficiency: 16.8,
//     price: 199.99,
//     stock: 32,
//     minStock: 15,
//     voltage: 24,
//     dimensions: '1650x992x35mm',
//     warranty: '20 years',
//     status: 'active',
//     sku: 'SP-POLY-250W',
//   },
//   {
//     id: 3,
//     name: 'Flexible Solar Panel 100W',
//     wattage: 100,
//     efficiency: 18.2,
//     price: 149.99,
//     stock: 8,
//     minStock: 10,
//     voltage: 12,
//     dimensions: '1200x540x3mm',
//     warranty: '10 years',
//     status: 'active',
//     sku: 'SP-FLEX-100W',
//   },
// ];

// export default function SolarPanelsPage() {
//   return (
//     <AdminLayout 
//       title="Solar Panels" 
//       subtitle="Manage photovoltaic panels and solar modules"
//     >
//       <div className="space-y-8">
//         {/* Header Actions */}
//         <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
//           <div className="flex flex-col sm:flex-row gap-4 flex-1">
//             <div className="relative flex-1 max-w-md">
//               <Input
//                 type="search"
//                 placeholder="Search solar panels..."
//                 leftIcon={<Search className="h-4 w-4" />}
//                 className="pl-10"
//               />
//             </div>
//             <Button variant="outline" className="whitespace-nowrap">
//               <Filter className="h-4 w-4 mr-2" />
//               Filter by Wattage
//             </Button>
//           </div>
//           <Button variant="default" className="whitespace-nowrap">
//             <Plus className="h-4 w-4 mr-2" />
//             Add Solar Panel
//           </Button>
//         </div>

//         {/* Solar Panels Grid */}
//         <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
//           {solarPanels.map((panel) => {
//             const stockStatus = getStockStatus(panel.stock, panel.minStock);
            
//             return (
//               <Card key={panel.id} className="group hover:shadow-strong transition-all duration-200">
//                 <CardContent className="p-6">
//                   <div className="space-y-4">
//                     {/* Panel Image */}
//                     <div className="aspect-video bg-gradient-to-br from-blue-50 to-yellow-50 rounded-lg flex items-center justify-center border-2 border-dashed border-neutral-200">
//                       <Sun className="h-16 w-16 text-yellow-500" />
//                     </div>
                    
//                     {/* Panel Info */}
//                     <div className="space-y-3">
//                       <div className="flex items-start justify-between">
//                         <h3 className="font-semibold text-neutral-900 line-clamp-2">
//                           {panel.name}
//                         </h3>
//                         <Badge variant="success" size="sm">
//                           {panel.status}
//                         </Badge>
//                       </div>
                      
//                       <div className="grid grid-cols-2 gap-3 text-sm">
//                         <div className="flex items-center gap-2">
//                           <Zap className="h-4 w-4 text-yellow-600" />
//                           <span className="font-medium">{panel.wattage}W</span>
//                         </div>
//                         <div className="flex items-center gap-2">
//                           <Settings className="h-4 w-4 text-blue-600" />
//                           <span className="font-medium">{panel.efficiency}%</span>
//                         </div>
//                       </div>
                      
//                       <div className="space-y-2 text-sm text-neutral-600">
//                         <p><span className="font-medium">Voltage:</span> {panel.voltage}V</p>
//                         <p><span className="font-medium">Dimensions:</span> {panel.dimensions}</p>
//                         <p><span className="font-medium">Warranty:</span> {panel.warranty}</p>
//                         <p><span className="font-medium">SKU:</span> {panel.sku}</p>
//                       </div>
                      
//                       <div className="flex items-center justify-between pt-2 border-t border-neutral-200">
//                         <span className="text-xl font-bold text-neutral-900">
//                           {formatCurrency(panel.price)}
//                         </span>
//                         <Badge 
//                           variant={stockStatus.status === 'in-stock' ? 'success' : 'warning'}
//                           size="sm"
//                         >
//                           {panel.stock} in stock
//                         </Badge>
//                       </div>
//                     </div>
                    
//                     {/* Actions */}
//                     <div className="flex gap-2 pt-2">
//                       <Button variant="outline" size="sm" className="flex-1">
//                         <Eye className="h-3 w-3 mr-1" />
//                         View
//                       </Button>
//                       <Button variant="outline" size="sm" className="flex-1">
//                         <Edit className="h-3 w-3 mr-1" />
//                         Edit
//                       </Button>
//                       <Button variant="outline" size="sm">
//                         <Trash2 className="h-3 w-3" />
//                       </Button>
//                     </div>
//                   </div>
//                 </CardContent>
//               </Card>
//             );
//           })}
//         </div>

//         {/* Panel Statistics */}
//         <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
//           <Card>
//             <CardContent className="p-6">
//               <div className="flex items-center justify-between">
//                 <div>
//                   <p className="text-sm font-medium text-neutral-600">Total Panels</p>
//                   <p className="text-2xl font-bold text-neutral-900">{solarPanels.length}</p>
//                 </div>
//                 <div className="h-12 w-12 bg-yellow-100 rounded-lg flex items-center justify-center">
//                   <Sun className="h-6 w-6 text-yellow-600" />
//                 </div>
//               </div>
//             </CardContent>
//           </Card>

//           <Card>
//             <CardContent className="p-6">
//               <div className="flex items-center justify-between">
//                 <div>
//                   <p className="text-sm font-medium text-neutral-600">Total Wattage</p>
//                   <p className="text-2xl font-bold text-neutral-900">
//                     {solarPanels.reduce((sum, panel) => sum + (panel.wattage * panel.stock), 0).toLocaleString()}W
//                   </p>
//                 </div>
//                 <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
//                   <Zap className="h-6 w-6 text-blue-600" />
//                 </div>
//               </div>
//             </CardContent>
//           </Card>

//           <Card>
//             <CardContent className="p-6">
//               <div className="flex items-center justify-between">
//                 <div>
//                   <p className="text-sm font-medium text-neutral-600">Avg Efficiency</p>
//                   <p className="text-2xl font-bold text-neutral-900">
//                     {(solarPanels.reduce((sum, panel) => sum + panel.efficiency, 0) / solarPanels.length).toFixed(1)}%
//                   </p>
//                 </div>
//                 <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
//                   <Settings className="h-6 w-6 text-green-600" />
//                 </div>
//               </div>
//             </CardContent>
//           </Card>

//           <Card>
//             <CardContent className="p-6">
//               <div className="flex items-center justify-between">
//                 <div>
//                   <p className="text-sm font-medium text-neutral-600">Total Value</p>
//                   <p className="text-2xl font-bold text-neutral-900">
//                     {formatCurrency(solarPanels.reduce((sum, panel) => sum + (panel.price * panel.stock), 0))}
//                   </p>
//                 </div>
//                 <div className="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center">
//                   <Sun className="h-6 w-6 text-primary-600" />
//                 </div>
//               </div>
//             </CardContent>
//           </Card>
//         </div>
//       </div>
//     </AdminLayout>
//   );
// }

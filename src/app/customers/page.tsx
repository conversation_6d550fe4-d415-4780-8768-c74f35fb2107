'use client';

import React, { useState, useMemo } from 'react';
import { AdminLayout } from '@/components/layout/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import { Modal } from '@/components/ui/Modal';
import { ConfirmationModal } from '@/components/ui/ConfirmationModal';
import { useToast } from '@/components/ui/Toast';
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
  Pagination,
} from '@/components/ui/Table';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  DollarSign
} from 'lucide-react';
import { formatCurrency, formatDate } from '@/lib/utils';
import { CustomerForm } from '@/components/customers/CustomerForm';
import { CustomerDetailModal } from '@/components/customers/CustomerDetailModal';

interface Customer {
  id: number;
  name: string;
  email: string;
  phone: string;
  address: string;
  joinDate: string;
  totalOrders: number;
  totalSpent: number;
  status: 'active' | 'inactive' | 'vip' | 'new';
  lastOrder: string;
}

interface CustomerFormData {
  name: string;
  email: string;
  phone: string;
  address: string;
  status: 'active' | 'inactive' | 'vip' | 'new';
}

const mockCustomers: Customer[] = [
  {
    id: 1,
    name: 'John Smith',
    email: '<EMAIL>',
    phone: '+****************',
    address: '123 Main St, Springfield, IL 62701',
    joinDate: '2024-01-15',
    totalOrders: 5,
    totalSpent: 12450.00,
    status: 'active',
    lastOrder: '2024-03-10',
  },
  {
    id: 2,
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    phone: '+****************',
    address: '456 Oak Ave, Chicago, IL 60601',
    joinDate: '2024-02-20',
    totalOrders: 3,
    totalSpent: 8900.00,
    status: 'active',
    lastOrder: '2024-03-08',
  },
  {
    id: 3,
    name: 'Mike Davis',
    email: '<EMAIL>',
    phone: '+****************',
    address: '789 Pine Rd, Austin, TX 73301',
    joinDate: '2023-11-10',
    totalOrders: 8,
    totalSpent: 25600.00,
    status: 'vip',
    lastOrder: '2024-03-12',
  },
  {
    id: 4,
    name: 'Emily Wilson',
    email: '<EMAIL>',
    phone: '+****************',
    address: '321 Elm St, Denver, CO 80201',
    joinDate: '2024-03-01',
    totalOrders: 1,
    totalSpent: 899.99,
    status: 'new',
    lastOrder: '2024-03-01',
  },
];

const getStatusVariant = (status: Customer['status']) => {
  switch (status) {
    case 'vip':
      return 'default';
    case 'active':
      return 'success';
    case 'new':
      return 'secondary';
    case 'inactive':
      return 'warning';
    default:
      return 'secondary';
  }
};

export default function CustomersPage() {
  const [customers, setCustomers] = useState<Customer[]>(mockCustomers);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const { addToast } = useToast();
  const itemsPerPage = 10;

  // Filter customers based on search term
  const filteredCustomers = useMemo(() => {
    if (!searchTerm) return customers;

    return customers.filter(customer =>
      customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.phone.includes(searchTerm) ||
      customer.address.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [customers, searchTerm]);

  // Paginate filtered customers
  const paginatedCustomers = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredCustomers.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredCustomers, currentPage]);

  const totalPages = Math.ceil(filteredCustomers.length / itemsPerPage);

  // Reset to first page when search changes
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  const openCreateModal = () => setIsCreateModalOpen(true);
  const closeCreateModal = () => setIsCreateModalOpen(false);

  const openEditModal = (customer: Customer) => {
    setSelectedCustomer(customer);
    setIsEditModalOpen(true);
  };
  const closeEditModal = () => {
    setSelectedCustomer(null);
    setIsEditModalOpen(false);
  };

  const openViewModal = (customer: Customer) => {
    setSelectedCustomer(customer);
    setIsViewModalOpen(true);
  };
  const closeViewModal = () => {
    setSelectedCustomer(null);
    setIsViewModalOpen(false);
  };

  const openDeleteModal = (customer: Customer) => {
    setSelectedCustomer(customer);
    setIsDeleteModalOpen(true);
  };
  const closeDeleteModal = () => {
    setSelectedCustomer(null);
    setIsDeleteModalOpen(false);
  };

  const handleCreateCustomer = async (customerData: CustomerFormData) => {
    setIsLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      const newCustomer: Customer = {
        id: customers.length + 1,
        ...customerData,
        joinDate: new Date().toISOString().split('T')[0],
        totalOrders: 0,
        totalSpent: 0,
        lastOrder: '',
      };

      setCustomers(prev => [newCustomer, ...prev]);

      addToast({
        type: 'success',
        title: 'Customer Created',
        message: `${customerData.name} has been added successfully.`,
      });

      closeCreateModal();
    } catch (error) {
      console.error('Create customer error:', error);
      addToast({
        type: 'error',
        title: 'Creation Failed',
        message: 'Failed to create customer. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditCustomer = async (customerData: CustomerFormData) => {
    if (!selectedCustomer) return;

    setIsLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      setCustomers(prev => prev.map(customer =>
        customer.id === selectedCustomer.id
          ? { ...customer, ...customerData }
          : customer
      ));

      addToast({
        type: 'success',
        title: 'Customer Updated',
        message: `${customerData.name} has been updated successfully.`,
      });

      closeEditModal();
    } catch (error) {
      console.error('Update customer error:', error);
      addToast({
        type: 'error',
        title: 'Update Failed',
        message: 'Failed to update customer. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteCustomer = async () => {
    if (!selectedCustomer) return;

    setIsDeleting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      setCustomers(prev => prev.filter(customer => customer.id !== selectedCustomer.id));

      addToast({
        type: 'success',
        title: 'Customer Deleted',
        message: `${selectedCustomer.name} has been deleted successfully.`,
      });

      closeDeleteModal();
    } catch (error) {
      console.error('Delete customer error:', error);
      addToast({
        type: 'error',
        title: 'Deletion Failed',
        message: 'Failed to delete customer. Please try again.',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <AdminLayout
      title="Customers"
      subtitle="Manage your customer relationships and data"
    >
      <div className="space-y-6">
        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Total Customers</p>
                  <p className="text-2xl font-bold text-neutral-900">{customers.length}</p>
                </div>
                <div className="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center">
                  <User className="h-6 w-6 text-primary-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Active Customers</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {customers.filter(c => c.status === 'active').length}
                  </p>
                </div>
                <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <User className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">VIP Customers</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {customers.filter(c => c.status === 'vip').length}
                  </p>
                </div>
                <div className="h-12 w-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <User className="h-6 w-6 text-yellow-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Total Revenue</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {formatCurrency(customers.reduce((sum, c) => sum + c.totalSpent, 0))}
                  </p>
                </div>
                <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Table Header */}
        <Card>
          <CardHeader className="pb-4">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <CardTitle className="text-xl font-semibold">
                Customers ({filteredCustomers.length})
              </CardTitle>
              <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
                <div className="relative">
                  <Input
                    type="search"
                    placeholder="Search customers..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    leftIcon={<Search className="h-4 w-4" />}
                    className="w-full sm:w-64"
                  />
                </div>
                <Button onClick={openCreateModal}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Customer
                </Button>
              </div>
            </div>
          </CardHeader>

          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Customer</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Orders</TableHead>
                  <TableHead>Total Spent</TableHead>
                  <TableHead>Join Date</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedCustomers.map((customer) => (
                  <TableRow key={customer.id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <div className="h-10 w-10 bg-primary-100 rounded-full flex items-center justify-center">
                          <User className="h-5 w-5 text-primary-600" />
                        </div>
                        <div>
                          <div className="font-medium text-neutral-900">{customer.name}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <Mail className="h-4 w-4 text-neutral-400" />
                          <span className="text-sm text-neutral-900">{customer.email}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Phone className="h-4 w-4 text-neutral-400" />
                          <span className="text-sm text-neutral-600">{customer.phone}</span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getStatusVariant(customer.status)} size="sm">
                        {customer.status.toUpperCase()}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div className="font-medium text-neutral-900">{customer.totalOrders}</div>
                        <div className="text-neutral-600">
                          Last: {customer.lastOrder ? formatDate(customer.lastOrder) : 'Never'}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium text-neutral-900">{formatCurrency(customer.totalSpent)}</div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm text-neutral-600">{formatDate(customer.joinDate)}</div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openViewModal(customer)}
                        >
                          <Eye className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openEditModal(customer)}
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openDeleteModal(customer)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {/* Empty State */}
            {filteredCustomers.length === 0 && (
              <div className="text-center py-12">
                <User className="h-12 w-12 text-neutral-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-neutral-900 mb-2">
                  {searchTerm ? 'No customers found' : 'No customers yet'}
                </h3>
                <p className="text-neutral-600 mb-4">
                  {searchTerm
                    ? 'Try adjusting your search terms.'
                    : 'Get started by adding your first customer.'
                  }
                </p>
                {!searchTerm && (
                  <Button onClick={openCreateModal}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Customer
                  </Button>
                )}
              </div>
            )}

            {/* Pagination */}
            {filteredCustomers.length > 0 && (
              <div className="px-6 py-4 border-t border-neutral-200">
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={setCurrentPage}
                  itemsPerPage={itemsPerPage}
                  totalItems={filteredCustomers.length}
                />
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Create Customer Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={closeCreateModal}
        title="Add New Customer"
        description="Create a new customer profile"
        size="lg"
      >
        <CustomerForm
          onSubmit={handleCreateCustomer}
          onCancel={closeCreateModal}
          isLoading={isLoading}
          mode="create"
        />
      </Modal>

      {/* Edit Customer Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={closeEditModal}
        title="Edit Customer"
        description="Update customer information"
        size="lg"
      >
        {selectedCustomer && (
          <CustomerForm
            initialData={{
              name: selectedCustomer.name,
              email: selectedCustomer.email,
              phone: selectedCustomer.phone,
              address: selectedCustomer.address,
              status: selectedCustomer.status,
            }}
            onSubmit={handleEditCustomer}
            onCancel={closeEditModal}
            isLoading={isLoading}
            mode="edit"
          />
        )}
      </Modal>

      {/* View Customer Modal */}
      <CustomerDetailModal
        isOpen={isViewModalOpen}
        onClose={closeViewModal}
        customer={selectedCustomer}
      />

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={closeDeleteModal}
        onConfirm={handleDeleteCustomer}
        title="Delete Customer"
        message={`Are you sure you want to delete "${selectedCustomer?.name}"? This action cannot be undone and will remove all associated data.`}
        confirmText="Delete"
        cancelText="Cancel"
        variant="danger"
        isLoading={isDeleting}
      />
    </AdminLayout>
  );
}

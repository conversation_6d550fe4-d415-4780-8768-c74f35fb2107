import { AdminLayout } from '@/components/layout/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import {
  MdInventory,
  MdShoppingCart,
  MdPeople,
  MdTrendingUp,
  MdWarning,
  MdAdd,
  MdVisibility
} from 'react-icons/md';
import { formatCurrency, formatNumber } from '@/lib/utils';

// Mock data for dashboard
const stats = [
  {
    title: 'Total Products',
    value: 1247,
    change: '+12%',
    changeType: 'positive' as const,
    icon: MdInventory,
    color: 'bg-blue-500',
  },
  {
    title: 'Total Orders',
    value: 89,
    change: '+23%',
    changeType: 'positive' as const,
    icon: MdShoppingCart,
    color: 'bg-green-500',
  },
  {
    title: 'Customers',
    value: 2456,
    change: '+5%',
    changeType: 'positive' as const,
    icon: MdPeople,
    color: 'bg-purple-500',
  },
  {
    title: 'Revenue',
    value: 45678,
    change: '+18%',
    changeType: 'positive' as const,
    icon: MdTrendingUp,
    format: 'currency' as const,
    color: 'bg-orange-500',
  },
];

const lowStockProducts = [
  { name: 'Solar Panel 300W', stock: 5, category: 'Solar Panels' },
  { name: 'Lithium Battery 100Ah', stock: 2, category: 'Batteries' },
  { name: 'MPPT Controller 40A', stock: 8, category: 'Controllers' },
];

const recentOrders = [
  { id: '#ORD-001', customer: 'John Smith', amount: 2450, status: 'completed' },
  { id: '#ORD-002', customer: 'Sarah Johnson', amount: 1890, status: 'processing' },
  { id: '#ORD-003', customer: 'Mike Davis', amount: 3200, status: 'pending' },
];

export default function Dashboard() {
  return (
    <AdminLayout
      title="Dashboard"
      subtitle="Welcome back! Here's what's happening with your Ecopulse business."
    >
      <div className="space-y-6">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat) => (
            <Card key={stat.title} className="hover:shadow-lg transition-shadow duration-200">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-3">
                      <div className={`h-10 w-10 ${stat.color} rounded-lg flex items-center justify-center shadow-sm`}>
                        <stat.icon className="h-5 w-5 text-white" />
                      </div>
                      <p className="text-sm font-medium text-neutral-600 truncate">
                        {stat.title}
                      </p>
                    
                    </div>
                    <div className="space-y-1">
                      <p className="text-2xl font-bold text-neutral-900 leading-none">
                        {stat.format === 'currency'
                          ? formatCurrency(stat.value)
                          : formatNumber(stat.value)
                        }
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Low Stock Alert */}
          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center text-lg">
                  <div className="h-8 w-8 bg-warning-100 rounded-lg flex items-center justify-center mr-3">
                    <MdWarning className="h-4 w-4 text-warning-600" />
                  </div>
                  Low Stock Alert
                </CardTitle>
                <Button variant="outline" size="sm">
                  <MdVisibility className="h-4 w-4 mr-2" />
                  View All
                </Button>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-4">
                {lowStockProducts.map((product, index) => (
                  <div key={product.name} className={`flex items-center justify-between py-3 ${index !== lowStockProducts.length - 1 ? 'border-b border-neutral-100' : ''}`}>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-neutral-900 truncate">{product.name}</p>
                      <p className="text-sm text-neutral-500">{product.category}</p>
                    </div>
                    <div className="ml-4">
                      <Badge variant="warning" className="font-medium">
                        {product.stock} left
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recent Orders */}
          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center text-lg">
                  <div className="h-8 w-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                    <MdShoppingCart className="h-4 w-4 text-blue-600" />
                  </div>
                  Recent Orders
                </CardTitle>
                <Button variant="outline" size="sm">
                  <MdVisibility className="h-4 w-4 mr-2" />
                  View All
                </Button>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-4">
                {recentOrders.map((order, index) => (
                  <div key={order.id} className={`flex items-center justify-between py-3 ${index !== recentOrders.length - 1 ? 'border-b border-neutral-100' : ''}`}>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-neutral-900">{order.id}</p>
                      <p className="text-sm text-neutral-500">{order.customer}</p>
                    </div>
                    <div className="text-right ml-4">
                      <p className="font-semibold text-neutral-900 mb-1">
                        {formatCurrency(order.amount)}
                      </p>
                      <Badge
                        variant={
                          order.status === 'completed' ? 'success' :
                          order.status === 'processing' ? 'warning' : 'secondary'
                        }
                        className="font-medium"
                      >
                        {order.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

     
      </div>
    </AdminLayout>
  );
}

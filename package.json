{"name": "ecopluse-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-toastify": "^11.0.5", "tailwind-merge": "^2.5.5", "clsx": "^2.1.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.19", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "framer-motion": "^12.10.0", "globals": "^16.0.0", "postcss": "^8.4.38", "react-icons": "^5.5.0", "react-router-dom": "^7.5.3", "tailwindcss": "^3.4.1", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}
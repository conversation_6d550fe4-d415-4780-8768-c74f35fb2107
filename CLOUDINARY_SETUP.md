# Cloudinary Setup Instructions

## Upload Preset Configuration

To enable unsigned uploads from the client-side, you need to create an upload preset in your Cloudinary dashboard.

### Steps:

1. **Login to Cloudinary Dashboard**
   - Go to https://cloudinary.com/console
   - Login with your account credentials

2. **Navigate to Upload Presets**
   - Go to Settings → Upload
   - Click on "Upload presets" tab

3. **Create New Upload Preset**
   - Click "Add upload preset"
   - Set the following configuration:

   **Basic Settings:**
   - Preset name: `ecopulse_products`
   - Signing mode: `Unsigned`
   - Folder: `ecopulse/products` (optional, for organization)

   **Upload Manipulations:**
   - Quality: `Auto`
   - Format: `Auto`
   - Max file size: `10MB`
   - Max image width: `2000px`
   - Max image height: `2000px`

   **Access Control:**
   - Resource type: `Image`
   - Allowed formats: `jpg,png,gif,webp`

4. **Save the Preset**
   - Click "Save" to create the preset

## Environment Variables

Make sure your `.env` file contains the following Cloudinary credentials:

```env
VITE_CLOUDINARY_CLOUD_NAME=dln68igvv
VITE_CLOUDINARY_API_KEY=***************
VITE_CLOUDINARY_API_SECRET=8cT3csX69YFobb3llqe8g-7W3Nc
```

## Security Notes

- The API secret is included for potential future server-side operations
- Client-side uploads use unsigned presets for security
- For production, consider implementing server-side upload signing
- Monitor your Cloudinary usage to prevent abuse

## Testing

After setup, you can test the upload functionality by:
1. Creating a new product
2. Uploading images through the product form
3. Verifying images appear in your Cloudinary media library
4. Checking that product images display correctly in the admin interface

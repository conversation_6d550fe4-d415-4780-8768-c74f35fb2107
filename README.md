# Ecopulse Admin

A modern, responsive admin panel for solar equipment management built with Next.js 15, TypeScript, and Tailwind CSS. Designed specifically for Ecopulse's solar business operations.

## Features

- **Modern Design**: Clean, professional interface with Ecopulse branding
- **Responsive Layout**: Works seamlessly on desktop, tablet, and mobile devices
- **Product Management**: Comprehensive solar equipment catalog with categories
  - Solar Panels with efficiency ratings and wattage specifications
  - Batteries with capacity and chemistry details
  - Inverters with power conversion specifications
  - Charge Controllers with MPPT/PWM technology
- **Order Tracking**: Complete order management system with status tracking
- **Customer Management**: Customer relationship management tools
- **Analytics Dashboard**: Business insights and performance metrics
- **Inventory Management**: Stock tracking with low-stock alerts
- **Settings Panel**: User preferences and system configuration
- **Type Safety**: Built with TypeScript for better development experience

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **Components**: Custom UI component library
- **State Management**: React hooks and context
- **Build Tool**: Turbopack for fast development

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd ecopulse-admin
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Available Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript compiler check

## Development Status

✅ **Completed Features:**
- All core pages implemented
- Product management for all solar equipment types
- Customer and order management
- Analytics dashboard
- Settings panel
- Responsive design
- TypeScript integration
- Build optimization

---

